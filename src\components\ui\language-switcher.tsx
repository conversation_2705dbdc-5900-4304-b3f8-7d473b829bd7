"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Globe, Check } from 'lucide-react';
import { locales, localeNames, localeFlags, type Locale } from '@/lib/i18n';
import { cn } from '@/lib/utils';

interface LanguageSwitcherProps {
  currentLocale?: Locale;
  onLocaleChange?: (locale: Locale) => void;
}

export function LanguageSwitcher({ 
  currentLocale = 'en', 
  onLocaleChange 
}: LanguageSwitcherProps) {
  const [isOpen, setIsOpen] = useState(false);

  const handleLocaleChange = (locale: Locale) => {
    onLocaleChange?.(locale);
    setIsOpen(false);
    
    // Store in localStorage
    localStorage.setItem('preferred-locale', locale);
    
    // You could also update URL or make API call here
    console.log('Locale changed to:', locale);
  };

  return (
    <div className="relative">
      <Button
        variant="ghost"
        size="icon"
        onClick={() => setIsOpen(!isOpen)}
        className="h-9 w-9"
      >
        <Globe className="h-4 w-4" />
        <span className="sr-only">Change language</span>
      </Button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-40"
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute right-0 top-full mt-2 z-50 w-48 rounded-md border bg-popover p-1 shadow-md">
            <div className="px-2 py-1.5 text-sm font-semibold text-muted-foreground">
              Select Language
            </div>
            <div className="space-y-1">
              {locales.map((locale) => (
                <button
                  key={locale}
                  onClick={() => handleLocaleChange(locale)}
                  className={cn(
                    "flex w-full items-center justify-between rounded-sm px-2 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground",
                    currentLocale === locale && "bg-accent text-accent-foreground"
                  )}
                >
                  <div className="flex items-center space-x-2">
                    <span className="text-base">{localeFlags[locale]}</span>
                    <span>{localeNames[locale]}</span>
                  </div>
                  {currentLocale === locale && (
                    <Check className="h-4 w-4" />
                  )}
                </button>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  );
}
