import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { AnnouncementDetail } from '@/components/announcements/announcement-detail';

interface Props {
  params: { id: string };
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  try {
    const response = await fetch(`${process.env.APP_URL || 'http://localhost:3000'}/api/announcements/${params.id}`, {
      cache: 'no-store'
    });
    
    if (!response.ok) {
      return {
        title: 'Announcement Not Found - Community Platform',
      };
    }

    const result = await response.json();
    const announcement = result.data;

    return {
      title: `${announcement.title} - Community Platform`,
      description: announcement.excerpt || announcement.content.replace(/<[^>]*>/g, '').substring(0, 160),
      openGraph: {
        title: announcement.title,
        description: announcement.excerpt || announcement.content.replace(/<[^>]*>/g, '').substring(0, 160),
        type: 'article',
        publishedTime: announcement.createdAt,
        authors: [announcement.author.username],
      },
    };
  } catch (error) {
    return {
      title: 'Announcement - Community Platform',
    };
  }
}

export default async function AnnouncementPage({ params }: Props) {
  try {
    const response = await fetch(`${process.env.APP_URL || 'http://localhost:3000'}/api/announcements/${params.id}`, {
      cache: 'no-store'
    });

    if (!response.ok) {
      notFound();
    }

    const result = await response.json();
    
    if (!result.success) {
      notFound();
    }

    return (
      <div className="container py-8">
        <div className="max-w-4xl mx-auto">
          <AnnouncementDetail announcement={result.data} />
        </div>
      </div>
    );
  } catch (error) {
    notFound();
  }
}
