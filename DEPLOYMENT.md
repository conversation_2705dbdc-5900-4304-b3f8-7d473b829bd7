# Community Platform Deployment Guide

This guide will help you deploy the Community Platform to production.

## Prerequisites

Before deploying, ensure you have:

- Node.js 18+ installed
- A PostgreSQL database (for production)
- A domain name (optional)
- SSL certificate (recommended)

## Installation Steps

### 1. Install Node.js

Download and install Node.js from [nodejs.org](https://nodejs.org/)

For Windows:
```powershell
# Using Chocolatey
choco install nodejs

# Or download from website and run installer
```

For Linux/macOS:
```bash
# Using nvm (recommended)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18
```

### 2. Clone and Setup Project

```bash
# Clone the repository
git clone <your-repository-url>
cd community-platform

# Install dependencies
npm install

# Copy environment file
cp .env.example .env.local
```

### 3. Configure Environment Variables

Edit `.env.local` with your settings:

```env
# Database (Use PostgreSQL for production)
DATABASE_URL="postgresql://username:password@localhost:5432/community_platform"

# NextAuth.js
NEXTAUTH_URL="https://yourdomain.com"
NEXTAUTH_SECRET="your-super-secret-key-here"

# File Upload
UPLOAD_MAX_SIZE=50000000
UPLOAD_DIR="./public/uploads"
AVATAR_DIR="./public/avatars"

# Admin Settings
ADMIN_EMAIL="<EMAIL>"
ADMIN_USERNAME="admin"
ADMIN_PASSWORD="secure-admin-password"

# Application Settings
APP_NAME="Your Community Platform"
APP_URL="https://yourdomain.com"
APP_DESCRIPTION="Your community description"

# Security
JWT_SECRET="your-jwt-secret-here"
BCRYPT_ROUNDS=12

# Email (Optional)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
```

### 4. Database Setup

For PostgreSQL:
```bash
# Install PostgreSQL
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# Create database
sudo -u postgres createdb community_platform

# Create user
sudo -u postgres psql
CREATE USER your_username WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE community_platform TO your_username;
\q
```

### 5. Initialize Database

```bash
# Generate Prisma client
npx prisma generate

# Run database migrations
npx prisma db push

# (Optional) Seed database with sample data
npx prisma db seed
```

### 6. Build and Start

```bash
# Build the application
npm run build

# Start production server
npm start
```

## Production Deployment Options

### Option 1: Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Configure environment variables in Vercel dashboard
4. Deploy automatically

### Option 2: VPS/Dedicated Server

#### Using PM2 (Process Manager)

```bash
# Install PM2 globally
npm install -g pm2

# Create ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'community-platform',
    script: 'npm',
    args: 'start',
    cwd: '/path/to/your/app',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
}
EOF

# Start with PM2
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 to start on boot
pm2 startup
```

#### Using Docker

```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000

CMD ["npm", "start"]
```

```bash
# Build and run
docker build -t community-platform .
docker run -p 3000:3000 --env-file .env.local community-platform
```

### Option 3: Nginx Reverse Proxy

```nginx
# /etc/nginx/sites-available/community-platform
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Static files
    location /_next/static {
        alias /path/to/your/app/.next/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Uploaded files
    location /uploads {
        alias /path/to/your/app/public/uploads;
        expires 1y;
        add_header Cache-Control "public";
    }
}
```

## SSL Certificate Setup

### Using Let's Encrypt (Free)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get certificate
sudo certbot --nginx -d yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## Performance Optimization

### 1. Enable Compression

```nginx
# In nginx.conf
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
```

### 2. Database Optimization

```sql
-- Add indexes for better performance
CREATE INDEX idx_posts_category_id ON posts(category_id);
CREATE INDEX idx_posts_author_id ON posts(author_id);
CREATE INDEX idx_comments_post_id ON comments(post_id);
CREATE INDEX idx_files_uploaded_by ON files(uploaded_by);
CREATE INDEX idx_system_logs_created_at ON system_logs(created_at);
```

### 3. Caching

```bash
# Install Redis for caching
sudo apt install redis-server

# Configure in your app
REDIS_URL="redis://localhost:6379"
```

## Monitoring and Maintenance

### 1. Log Monitoring

```bash
# View application logs
pm2 logs community-platform

# View system logs
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
```

### 2. Database Backup

```bash
# Create backup script
cat > backup.sh << EOF
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump community_platform > backup_$DATE.sql
# Upload to cloud storage
EOF

chmod +x backup.sh

# Schedule daily backups
crontab -e
# Add: 0 2 * * * /path/to/backup.sh
```

### 3. Health Checks

```bash
# Create health check endpoint
curl https://yourdomain.com/api/health

# Monitor with uptime services like:
# - UptimeRobot
# - Pingdom
# - StatusCake
```

## Security Checklist

- [ ] Use HTTPS with valid SSL certificate
- [ ] Set strong passwords for admin accounts
- [ ] Configure firewall (UFW/iptables)
- [ ] Regular security updates
- [ ] Database access restrictions
- [ ] File upload restrictions
- [ ] Rate limiting enabled
- [ ] CORS properly configured
- [ ] Environment variables secured

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check DATABASE_URL format
   - Verify database is running
   - Check firewall settings

2. **File Upload Issues**
   - Check directory permissions
   - Verify UPLOAD_MAX_SIZE setting
   - Check disk space

3. **Performance Issues**
   - Monitor database queries
   - Check server resources
   - Review application logs

4. **SSL Certificate Issues**
   - Verify certificate validity
   - Check nginx configuration
   - Restart nginx service

### Getting Help

- Check application logs: `pm2 logs`
- Review nginx logs: `/var/log/nginx/`
- Database logs: `/var/log/postgresql/`
- System logs: `journalctl -u your-service`

## Scaling Considerations

For high-traffic deployments:

1. **Load Balancing**: Use multiple server instances
2. **Database Scaling**: Read replicas, connection pooling
3. **CDN**: Use CloudFlare or AWS CloudFront
4. **Caching**: Redis/Memcached for session and data caching
5. **File Storage**: AWS S3 or similar for uploaded files
6. **Monitoring**: Implement comprehensive monitoring with tools like Grafana

## Support

For deployment support:
- Email: <EMAIL>
- Documentation: Check README.md
- Issues: GitHub Issues page
