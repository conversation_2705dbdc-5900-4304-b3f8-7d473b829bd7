"use client";

import { useState, useEffect } from 'react';
import { useTheme } from 'next-themes';
import { Button } from '@/components/ui/button';
import { <PERSON>, Moon, Monitor, Check } from 'lucide-react';
import { cn } from '@/lib/utils';

const themes = [
  {
    name: 'light',
    label: 'Light',
    icon: Sun,
  },
  {
    name: 'dark',
    label: 'Dark',
    icon: Moon,
  },
  {
    name: 'system',
    label: 'System',
    icon: Monitor,
  },
] as const;

export function ThemeSwitcher() {
  const [isOpen, setIsOpen] = useState(false);
  const [mounted, setMounted] = useState(false);
  const { theme, setTheme } = useTheme();

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleThemeChange = (newTheme: string) => {
    setTheme(newTheme);
    setIsOpen(false);
  };

  const getCurrentThemeIcon = () => {
    if (!mounted) return Sun;
    
    const currentTheme = themes.find(t => t.name === theme);
    return currentTheme?.icon || Sun;
  };

  const CurrentIcon = getCurrentThemeIcon();

  return (
    <div className="relative">
      <Button
        variant="ghost"
        size="icon"
        onClick={() => setIsOpen(!isOpen)}
        className="h-9 w-9"
      >
        <CurrentIcon className="h-4 w-4" />
        <span className="sr-only">Toggle theme</span>
      </Button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-40"
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute right-0 top-full mt-2 z-50 w-36 rounded-md border bg-popover p-1 shadow-md">
            <div className="px-2 py-1.5 text-sm font-semibold text-muted-foreground">
              Theme
            </div>
            <div className="space-y-1">
              {themes.map((themeOption) => {
                const Icon = themeOption.icon;
                const isSelected = mounted && theme === themeOption.name;
                
                return (
                  <button
                    key={themeOption.name}
                    onClick={() => handleThemeChange(themeOption.name)}
                    className={cn(
                      "flex w-full items-center justify-between rounded-sm px-2 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground",
                      isSelected && "bg-accent text-accent-foreground"
                    )}
                  >
                    <div className="flex items-center space-x-2">
                      <Icon className="h-4 w-4" />
                      <span>{themeOption.label}</span>
                    </div>
                    {isSelected && (
                      <Check className="h-4 w-4" />
                    )}
                  </button>
                );
              })}
            </div>
          </div>
        </>
      )}
    </div>
  );
}
