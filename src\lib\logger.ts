import { db } from './db';
import { LogLevel } from '@/types';

interface LogEntry {
  level: LogLevel;
  message: string;
  details?: string;
  userId?: string;
  ip?: string;
  userAgent?: string;
}

class Logger {
  private isDevelopment = process.env.NODE_ENV === 'development';

  async log(entry: LogEntry): Promise<void> {
    // Console logging for development
    if (this.isDevelopment) {
      const timestamp = new Date().toISOString();
      const logMessage = `[${timestamp}] ${entry.level}: ${entry.message}`;
      
      switch (entry.level) {
        case LogLevel.ERROR:
          console.error(logMessage, entry.details);
          break;
        case LogLevel.WARN:
          console.warn(logMessage, entry.details);
          break;
        case LogLevel.INFO:
          console.info(logMessage, entry.details);
          break;
        case LogLevel.DEBUG:
          console.debug(logMessage, entry.details);
          break;
        default:
          console.log(logMessage, entry.details);
      }
    }

    // Database logging
    try {
      await db.systemLog.create({
        data: {
          level: entry.level,
          message: entry.message,
          details: entry.details,
          userId: entry.userId,
          ip: entry.ip,
          userAgent: entry.userAgent,
        },
      });
    } catch (error) {
      // Fallback to console if database logging fails
      console.error('Failed to write to database log:', error);
    }
  }

  async info(message: string, details?: string, context?: Partial<LogEntry>): Promise<void> {
    await this.log({
      level: LogLevel.INFO,
      message,
      details,
      ...context,
    });
  }

  async warn(message: string, details?: string, context?: Partial<LogEntry>): Promise<void> {
    await this.log({
      level: LogLevel.WARN,
      message,
      details,
      ...context,
    });
  }

  async error(message: string, details?: string, context?: Partial<LogEntry>): Promise<void> {
    await this.log({
      level: LogLevel.ERROR,
      message,
      details,
      ...context,
    });
  }

  async debug(message: string, details?: string, context?: Partial<LogEntry>): Promise<void> {
    await this.log({
      level: LogLevel.DEBUG,
      message,
      details,
      ...context,
    });
  }

  // User activity logging
  async logUserActivity(
    userId: string,
    action: string,
    details?: string,
    ip?: string,
    userAgent?: string
  ): Promise<void> {
    await this.info(`User activity: ${action}`, details, {
      userId,
      ip,
      userAgent,
    });
  }

  // Security logging
  async logSecurityEvent(
    event: string,
    details?: string,
    userId?: string,
    ip?: string,
    userAgent?: string
  ): Promise<void> {
    await this.warn(`Security event: ${event}`, details, {
      userId,
      ip,
      userAgent,
    });
  }

  // Performance logging
  async logPerformance(
    operation: string,
    duration: number,
    details?: string,
    userId?: string
  ): Promise<void> {
    const message = `Performance: ${operation} took ${duration}ms`;
    const level = duration > 5000 ? LogLevel.WARN : LogLevel.INFO;
    
    await this.log({
      level,
      message,
      details,
      userId,
    });
  }

  // Error logging with stack trace
  async logError(
    error: Error,
    context?: string,
    userId?: string,
    ip?: string,
    userAgent?: string
  ): Promise<void> {
    const message = context ? `${context}: ${error.message}` : error.message;
    const details = JSON.stringify({
      name: error.name,
      message: error.message,
      stack: error.stack,
      context,
    });

    await this.error(message, details, {
      userId,
      ip,
      userAgent,
    });
  }

  // Get recent logs
  async getRecentLogs(
    limit: number = 100,
    level?: LogLevel,
    userId?: string
  ): Promise<any[]> {
    try {
      const where: any = {};
      
      if (level) {
        where.level = level;
      }
      
      if (userId) {
        where.userId = userId;
      }

      return await db.systemLog.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        take: limit,
      });
    } catch (error) {
      console.error('Failed to fetch logs:', error);
      return [];
    }
  }

  // Clean old logs
  async cleanOldLogs(daysToKeep: number = 30): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      const result = await db.systemLog.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDate,
          },
        },
      });

      await this.info(`Cleaned ${result.count} old log entries`);
      return result.count;
    } catch (error) {
      await this.logError(error as Error, 'Failed to clean old logs');
      return 0;
    }
  }

  // Get log statistics
  async getLogStats(days: number = 7): Promise<any> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const stats = await db.systemLog.groupBy({
        by: ['level'],
        where: {
          createdAt: {
            gte: startDate,
          },
        },
        _count: {
          level: true,
        },
      });

      const total = await db.systemLog.count({
        where: {
          createdAt: {
            gte: startDate,
          },
        },
      });

      return {
        total,
        byLevel: stats.reduce((acc, stat) => {
          acc[stat.level] = stat._count.level;
          return acc;
        }, {} as Record<string, number>),
        period: `${days} days`,
      };
    } catch (error) {
      console.error('Failed to get log stats:', error);
      return {
        total: 0,
        byLevel: {},
        period: `${days} days`,
      };
    }
  }
}

export const logger = new Logger();

// Middleware helper for request logging
export function createRequestLogger() {
  return async (req: any, res: any, next: any) => {
    const start = Date.now();
    const { method, url, ip, headers } = req;
    const userAgent = headers['user-agent'];

    // Log request start
    await logger.info(`${method} ${url}`, undefined, {
      ip,
      userAgent,
    });

    // Override res.end to log response
    const originalEnd = res.end;
    res.end = function(...args: any[]) {
      const duration = Date.now() - start;
      const { statusCode } = res;

      // Log response
      logger.info(
        `${method} ${url} - ${statusCode}`,
        `Duration: ${duration}ms`,
        { ip, userAgent }
      );

      // Log slow requests
      if (duration > 1000) {
        logger.warn(
          `Slow request: ${method} ${url}`,
          `Duration: ${duration}ms, Status: ${statusCode}`,
          { ip, userAgent }
        );
      }

      originalEnd.apply(this, args);
    };

    if (next) next();
  };
}
