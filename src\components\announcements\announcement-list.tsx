"use client";

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Bell, Pin, Search, Eye, Calendar, User } from 'lucide-react';
import { formatRelativeTime } from '@/lib/utils';

interface Announcement {
  id: string;
  title: string;
  content: string;
  excerpt?: string;
  isPublished: boolean;
  isPinned: boolean;
  views: number;
  createdAt: string;
  updatedAt: string;
  author: {
    id: string;
    username: string;
    avatar?: string;
  };
}

export function AnnouncementList() {
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState('');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const fetchAnnouncements = async (reset = false) => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: reset ? '1' : page.toString(),
        limit: '10',
        search,
        published: 'true', // Only show published announcements
      });

      const response = await fetch(`/api/announcements?${params}`);
      const result = await response.json();

      if (result.success) {
        if (reset) {
          setAnnouncements(result.data);
          setPage(1);
        } else {
          setAnnouncements(prev => [...prev, ...result.data]);
        }
        setHasMore(result.pagination.hasNext);
      }
    } catch (error) {
      console.error('Error fetching announcements:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    fetchAnnouncements(true);
  };

  const loadMore = () => {
    setPage(prev => prev + 1);
    fetchAnnouncements();
  };

  useEffect(() => {
    fetchAnnouncements(true);
  }, []);

  return (
    <div className="space-y-6">
      {/* Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search announcements..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="pl-10"
                />
              </div>
            </div>
            <Button onClick={handleSearch} variant="outline">
              Search
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Announcements */}
      <div className="space-y-6">
        {announcements.map((announcement) => (
          <Card key={announcement.id} className="card-hover">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-2 mb-2">
                  <Bell className="h-5 w-5 text-primary" />
                  {announcement.isPinned && (
                    <Pin className="h-4 w-4 text-yellow-500" />
                  )}
                  <Badge variant={announcement.isPinned ? "default" : "secondary"}>
                    {announcement.isPinned ? "Pinned" : "Announcement"}
                  </Badge>
                </div>
                <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <Eye className="h-4 w-4" />
                    <span>{announcement.views}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-4 w-4" />
                    <span>{formatRelativeTime(announcement.createdAt)}</span>
                  </div>
                </div>
              </div>
              <CardTitle className="text-xl hover:text-primary transition-colors">
                <Link href={`/announcements/${announcement.id}`}>
                  {announcement.title}
                </Link>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-base leading-relaxed mb-4">
                {announcement.excerpt || announcement.content.replace(/<[^>]*>/g, '').substring(0, 200) + '...'}
              </CardDescription>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <User className="h-4 w-4" />
                  <span>By {announcement.author.username}</span>
                </div>
                
                <Button variant="ghost" size="sm" asChild>
                  <Link href={`/announcements/${announcement.id}`}>
                    Read More
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Load More */}
      {hasMore && (
        <div className="text-center">
          <Button 
            onClick={loadMore} 
            disabled={loading}
            variant="outline"
          >
            {loading ? 'Loading...' : 'Load More'}
          </Button>
        </div>
      )}

      {announcements.length === 0 && !loading && (
        <div className="text-center py-12">
          <Bell className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">No announcements found</h3>
          <p className="text-muted-foreground">
            {search ? 'Try adjusting your search criteria' : 'Check back later for updates'}
          </p>
        </div>
      )}
    </div>
  );
}
