"use client";

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MessageSquare, Users, TrendingUp } from 'lucide-react';
import { formatRelativeTime } from '@/lib/utils';

interface Category {
  id: string;
  name: string;
  description?: string;
  slug: string;
  color?: string;
  icon?: string;
  _count: {
    posts: number;
  };
  posts: Array<{
    id: string;
    title: string;
    slug: string;
    createdAt: string;
    author: {
      id: string;
      username: string;
      avatar?: string;
    };
    _count: {
      comments: number;
      likes: number;
    };
  }>;
}

export function ForumCategories() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/forum/categories');
        const result = await response.json();
        
        if (result.success) {
          setCategories(result.data);
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse space-y-3">
                <div className="h-4 bg-muted rounded w-1/4"></div>
                <div className="h-3 bg-muted rounded w-3/4"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold tracking-tight">Categories</h2>
        <Badge variant="secondary">
          {categories.length} categories
        </Badge>
      </div>

      <div className="space-y-4">
        {categories.map((category) => (
          <Card key={category.id} className="card-hover">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div 
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: category.color || '#3B82F6' }}
                  />
                  <div>
                    <CardTitle className="text-lg">
                      <Link 
                        href={`/forum/category/${category.slug}`}
                        className="hover:text-primary transition-colors"
                      >
                        {category.name}
                      </Link>
                    </CardTitle>
                    {category.description && (
                      <CardDescription className="mt-1">
                        {category.description}
                      </CardDescription>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <MessageSquare className="h-4 w-4" />
                    <span>{category._count.posts}</span>
                  </div>
                </div>
              </div>
            </CardHeader>
            
            {category.posts.length > 0 && (
              <CardContent className="pt-0">
                <div className="space-y-3">
                  <h4 className="text-sm font-medium text-muted-foreground">Recent Posts</h4>
                  {category.posts.slice(0, 3).map((post) => (
                    <div key={post.id} className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <Link 
                          href={`/forum/post/${post.slug}`}
                          className="text-sm font-medium hover:text-primary transition-colors truncate block"
                        >
                          {post.title}
                        </Link>
                        <div className="flex items-center space-x-2 text-xs text-muted-foreground mt-1">
                          <span>by {post.author.username}</span>
                          <span>•</span>
                          <span>{formatRelativeTime(post.createdAt)}</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3 text-xs text-muted-foreground ml-4">
                        <div className="flex items-center space-x-1">
                          <MessageSquare className="h-3 w-3" />
                          <span>{post._count.comments}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <TrendingUp className="h-3 w-3" />
                          <span>{post._count.likes}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            )}
          </Card>
        ))}
      </div>

      {categories.length === 0 && (
        <div className="text-center py-12">
          <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">No categories yet</h3>
          <p className="text-muted-foreground">
            Categories will appear here once they are created.
          </p>
        </div>
      )}
    </div>
  );
}
