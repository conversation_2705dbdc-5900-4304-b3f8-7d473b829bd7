import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON>, Users, MessageSquare, Upload, Bell } from "lucide-react";

export function Hero() {
  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-primary/10 via-background to-secondary/10">
      <div className="container relative z-10 py-24 lg:py-32">
        <div className="mx-auto max-w-4xl text-center">
          {/* Badge */}
          <div className="mb-8 inline-flex items-center rounded-full border px-4 py-2 text-sm">
            <span className="mr-2">🌟</span>
            <span className="font-medium">Welcome to our global community</span>
          </div>

          {/* Heading */}
          <h1 className="mb-6 text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl">
            Connect, Share,{" "}
            <span className="bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
              Grow Together
            </span>
          </h1>

          {/* Description */}
          <p className="mb-8 text-lg text-muted-foreground sm:text-xl lg:text-2xl max-w-3xl mx-auto">
            Join our vibrant community platform where people from around the world 
            connect, share files, discuss ideas, and stay updated with the latest announcements.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
            <Button size="lg" className="text-lg px-8 py-6" asChild>
              <Link href="/register">
                Get Started
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button variant="outline" size="lg" className="text-lg px-8 py-6" asChild>
              <Link href="/forum">
                Explore Forum
              </Link>
            </Button>
          </div>

          {/* Stats */}
          <div className="mt-16 grid grid-cols-2 gap-8 sm:grid-cols-4">
            <div className="text-center">
              <div className="mb-2 flex justify-center">
                <Users className="h-8 w-8 text-primary" />
              </div>
              <div className="text-2xl font-bold">10K+</div>
              <div className="text-sm text-muted-foreground">Active Users</div>
            </div>
            <div className="text-center">
              <div className="mb-2 flex justify-center">
                <MessageSquare className="h-8 w-8 text-primary" />
              </div>
              <div className="text-2xl font-bold">50K+</div>
              <div className="text-sm text-muted-foreground">Discussions</div>
            </div>
            <div className="text-center">
              <div className="mb-2 flex justify-center">
                <Upload className="h-8 w-8 text-primary" />
              </div>
              <div className="text-2xl font-bold">100K+</div>
              <div className="text-sm text-muted-foreground">Files Shared</div>
            </div>
            <div className="text-center">
              <div className="mb-2 flex justify-center">
                <Bell className="h-8 w-8 text-primary" />
              </div>
              <div className="text-2xl font-bold">24/7</div>
              <div className="text-sm text-muted-foreground">Support</div>
            </div>
          </div>
        </div>
      </div>

      {/* Background decoration */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
          <div className="h-[600px] w-[600px] rounded-full bg-gradient-to-r from-primary/20 to-secondary/20 blur-3xl" />
        </div>
      </div>
    </section>
  );
}
