"use client";

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MessageSquare, Users, TrendingUp, Clock } from 'lucide-react';

interface ForumStats {
  totalPosts: number;
  totalComments: number;
  totalUsers: number;
  activeUsers: number;
  recentActivity: Array<{
    type: 'post' | 'comment';
    title: string;
    user: string;
    createdAt: string;
  }>;
}

export function ForumStats() {
  const [stats, setStats] = useState<ForumStats>({
    totalPosts: 0,
    totalComments: 0,
    totalUsers: 0,
    activeUsers: 0,
    recentActivity: [],
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Mock data for now - in real app, fetch from API
    const mockStats: ForumStats = {
      totalPosts: 1247,
      totalComments: 3891,
      totalUsers: 892,
      activeUsers: 47,
      recentActivity: [
        {
          type: 'post',
          title: 'Welcome to our Community!',
          user: 'admin',
          createdAt: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
        },
        {
          type: 'comment',
          title: 'Great discussion about...',
          user: 'user123',
          createdAt: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
        },
        {
          type: 'post',
          title: 'Tips for new members',
          user: 'moderator',
          createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        },
        {
          type: 'comment',
          title: 'Thanks for sharing!',
          user: 'newbie',
          createdAt: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
        },
      ],
    };

    setTimeout(() => {
      setStats(mockStats);
      setLoading(false);
    }, 500);
  }, []);

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="p-6">
            <div className="animate-pulse space-y-4">
              <div className="h-4 bg-muted rounded w-1/2"></div>
              <div className="space-y-2">
                <div className="h-3 bg-muted rounded"></div>
                <div className="h-3 bg-muted rounded w-3/4"></div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Forum Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Forum Statistics</CardTitle>
          <CardDescription>
            Community activity overview
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{stats.totalPosts.toLocaleString()}</div>
              <div className="text-xs text-muted-foreground">Posts</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{stats.totalComments.toLocaleString()}</div>
              <div className="text-xs text-muted-foreground">Comments</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{stats.totalUsers.toLocaleString()}</div>
              <div className="text-xs text-muted-foreground">Members</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{stats.activeUsers}</div>
              <div className="text-xs text-muted-foreground">Online</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <Clock className="h-5 w-5 mr-2" />
            Recent Activity
          </CardTitle>
          <CardDescription>
            Latest forum activity
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {stats.recentActivity.map((activity, index) => (
              <div key={index} className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-1">
                  {activity.type === 'post' ? (
                    <MessageSquare className="h-4 w-4 text-blue-500" />
                  ) : (
                    <TrendingUp className="h-4 w-4 text-green-500" />
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="text-xs">
                      {activity.type}
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      {formatTimeAgo(activity.createdAt)}
                    </span>
                  </div>
                  <p className="text-sm font-medium truncate mt-1">
                    {activity.title}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    by {activity.user}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <button className="w-full text-left px-3 py-2 text-sm rounded-md hover:bg-accent transition-colors">
            📝 Create New Post
          </button>
          <button className="w-full text-left px-3 py-2 text-sm rounded-md hover:bg-accent transition-colors">
            🔍 Search Posts
          </button>
          <button className="w-full text-left px-3 py-2 text-sm rounded-md hover:bg-accent transition-colors">
            👥 View Members
          </button>
          <button className="w-full text-left px-3 py-2 text-sm rounded-md hover:bg-accent transition-colors">
            📊 Forum Rules
          </button>
        </CardContent>
      </Card>
    </div>
  );
}
