/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  images: {
    domains: ['localhost'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },
  // 支持文件上传
  api: {
    bodyParser: {
      sizeLimit: '50mb',
    },
  },
  // 国际化配置
  i18n: {
    locales: ['en', 'zh', 'ja', 'ko', 'es', 'fr', 'de', 'ru'],
    defaultLocale: 'en',
  },
}

module.exports = nextConfig
