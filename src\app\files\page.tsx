import { Metadata } from 'next';
import { FileUpload } from '@/components/upload/file-upload';
import { FileList } from '@/components/files/file-list';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export const metadata: Metadata = {
  title: 'Files - Community Platform',
  description: 'Upload and share files with the community. Browse images, videos, documents, and more.',
};

export default function FilesPage() {
  return (
    <div className="container py-8">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold tracking-tight mb-2">
            File Sharing
          </h1>
          <p className="text-lg text-muted-foreground">
            Upload and share files with the community. Support for images, videos, documents, and more.
          </p>
        </div>

        <Tabs defaultValue="browse" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 max-w-md">
            <TabsTrigger value="browse">Browse Files</TabsTrigger>
            <TabsTrigger value="upload">Upload Files</TabsTrigger>
          </TabsList>

          <TabsContent value="browse" className="space-y-6">
            <FileList />
          </TabsContent>

          <TabsContent value="upload" className="space-y-6">
            <FileUpload 
              multiple={true}
              onUploadComplete={(file) => {
                console.log('File uploaded:', file);
                // You could refresh the file list here
              }}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
