import { NextResponse } from 'next/server';
import { logger } from './logger';
import { ZodError } from 'zod';

export class AppError extends Error {
  public readonly statusCode: number;
  public readonly isOperational: boolean;
  public readonly code?: string;

  constructor(
    message: string,
    statusCode: number = 500,
    isOperational: boolean = true,
    code?: string
  ) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.code = code;

    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 400, true, 'VALIDATION_ERROR');
    this.name = 'ValidationError';
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication required') {
    super(message, 401, true, 'AUTHENTICATION_ERROR');
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, 403, true, 'AUTHORIZATION_ERROR');
    this.name = 'AuthorizationError';
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found') {
    super(message, 404, true, 'NOT_FOUND_ERROR');
    this.name = 'NotFoundError';
  }
}

export class ConflictError extends AppError {
  constructor(message: string = 'Resource conflict') {
    super(message, 409, true, 'CONFLICT_ERROR');
    this.name = 'ConflictError';
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Rate limit exceeded') {
    super(message, 429, true, 'RATE_LIMIT_ERROR');
    this.name = 'RateLimitError';
  }
}

export class DatabaseError extends AppError {
  constructor(message: string = 'Database operation failed') {
    super(message, 500, true, 'DATABASE_ERROR');
    this.name = 'DatabaseError';
  }
}

export class ExternalServiceError extends AppError {
  constructor(message: string = 'External service error') {
    super(message, 502, true, 'EXTERNAL_SERVICE_ERROR');
    this.name = 'ExternalServiceError';
  }
}

// Error handler for API routes
export async function handleApiError(
  error: unknown,
  request?: Request
): Promise<NextResponse> {
  let statusCode = 500;
  let message = 'Internal server error';
  let code = 'INTERNAL_ERROR';
  let details: any = undefined;

  // Extract request info for logging
  const requestInfo = request ? {
    method: request.method,
    url: request.url,
    headers: Object.fromEntries(request.headers.entries()),
  } : undefined;

  if (error instanceof AppError) {
    statusCode = error.statusCode;
    message = error.message;
    code = error.code || 'APP_ERROR';
  } else if (error instanceof ZodError) {
    statusCode = 400;
    message = 'Validation error';
    code = 'VALIDATION_ERROR';
    details = error.errors;
  } else if (error instanceof Error) {
    message = error.message;
    
    // Check for specific database errors
    if (error.message.includes('Unique constraint')) {
      statusCode = 409;
      message = 'Resource already exists';
      code = 'DUPLICATE_ERROR';
    } else if (error.message.includes('Foreign key constraint')) {
      statusCode = 400;
      message = 'Invalid reference';
      code = 'REFERENCE_ERROR';
    } else if (error.message.includes('Record to update not found')) {
      statusCode = 404;
      message = 'Resource not found';
      code = 'NOT_FOUND_ERROR';
    }
  }

  // Log the error
  await logger.logError(
    error instanceof Error ? error : new Error(String(error)),
    `API Error: ${request?.method} ${request?.url}`,
    undefined,
    requestInfo?.headers['x-forwarded-for'] || requestInfo?.headers['x-real-ip'],
    requestInfo?.headers['user-agent']
  );

  // Don't expose internal errors in production
  if (statusCode === 500 && process.env.NODE_ENV === 'production') {
    message = 'Internal server error';
    details = undefined;
  }

  return NextResponse.json(
    {
      success: false,
      error: message,
      code,
      details,
      timestamp: new Date().toISOString(),
    },
    { status: statusCode }
  );
}

// Global error handler for unhandled errors
export function setupGlobalErrorHandlers(): void {
  // Handle unhandled promise rejections
  process.on('unhandledRejection', async (reason: unknown, promise: Promise<any>) => {
    await logger.error(
      'Unhandled Promise Rejection',
      JSON.stringify({
        reason: reason instanceof Error ? reason.message : String(reason),
        stack: reason instanceof Error ? reason.stack : undefined,
        promise: promise.toString(),
      })
    );

    // In production, you might want to exit the process
    if (process.env.NODE_ENV === 'production') {
      console.error('Unhandled Promise Rejection. Shutting down...');
      process.exit(1);
    }
  });

  // Handle uncaught exceptions
  process.on('uncaughtException', async (error: Error) => {
    await logger.error(
      'Uncaught Exception',
      JSON.stringify({
        message: error.message,
        stack: error.stack,
        name: error.name,
      })
    );

    console.error('Uncaught Exception. Shutting down...');
    process.exit(1);
  });

  // Handle SIGTERM and SIGINT for graceful shutdown
  const gracefulShutdown = async (signal: string) => {
    await logger.info(`Received ${signal}. Starting graceful shutdown...`);
    
    // Close database connections, stop servers, etc.
    // Add your cleanup logic here
    
    await logger.info('Graceful shutdown completed');
    process.exit(0);
  };

  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
  process.on('SIGINT', () => gracefulShutdown('SIGINT'));
}

// Wrapper for async route handlers
export function asyncHandler(
  handler: (request: Request, context?: any) => Promise<NextResponse>
) {
  return async (request: Request, context?: any): Promise<NextResponse> => {
    try {
      return await handler(request, context);
    } catch (error) {
      return handleApiError(error, request);
    }
  };
}

// Client-side error boundary
export class ErrorBoundary {
  static async logClientError(
    error: Error,
    errorInfo?: any,
    userId?: string
  ): Promise<void> {
    try {
      await fetch('/api/errors/client', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: error.message,
          stack: error.stack,
          name: error.name,
          errorInfo,
          userId,
          url: window.location.href,
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString(),
        }),
      });
    } catch (logError) {
      console.error('Failed to log client error:', logError);
    }
  }
}

// Utility function to safely parse JSON
export function safeJsonParse<T>(json: string, fallback: T): T {
  try {
    return JSON.parse(json);
  } catch (error) {
    logger.warn('Failed to parse JSON', json);
    return fallback;
  }
}

// Utility function to safely execute async operations
export async function safeAsync<T>(
  operation: () => Promise<T>,
  fallback: T,
  errorMessage?: string
): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    if (errorMessage) {
      await logger.logError(
        error instanceof Error ? error : new Error(String(error)),
        errorMessage
      );
    }
    return fallback;
  }
}

// Rate limiting helper
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export function checkRateLimit(
  key: string,
  limit: number,
  windowMs: number
): boolean {
  const now = Date.now();
  const record = rateLimitMap.get(key);

  if (!record || now > record.resetTime) {
    rateLimitMap.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (record.count >= limit) {
    return false;
  }

  record.count++;
  return true;
}
