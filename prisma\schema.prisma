// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// 用户表
model User {
  id          String   @id @default(cuid())
  email       String   @unique
  username    String   @unique
  password    String
  firstName   String?
  lastName    String?
  avatar      String?
  bio         String?
  role        Role     @default(USER)
  isActive    Boolean  @default(true)
  language    String   @default("en")
  theme       String   @default("light")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  posts       Post[]
  comments    Comment[]
  likes       Like[]
  files       File[]
  announcements Announcement[]
  
  @@map("users")
}

// 用户角色枚举
enum Role {
  USER
  MODERATOR
  ADMIN
  SUPER_ADMIN
}

// 文件表
model File {
  id          String   @id @default(cuid())
  filename    String
  originalName String
  mimetype    String
  size        Int
  path        String
  url         String
  uploadedBy  String
  description String?
  isPublic    Boolean  @default(true)
  downloads   Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  user        User     @relation(fields: [uploadedBy], references: [id], onDelete: Cascade)
  
  @@map("files")
}

// 公告表
model Announcement {
  id          String   @id @default(cuid())
  title       String
  content     String
  excerpt     String?
  isPublished Boolean  @default(false)
  isPinned    Boolean  @default(false)
  authorId    String
  views       Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  author      User     @relation(fields: [authorId], references: [id], onDelete: Cascade)
  
  @@map("announcements")
}

// 论坛版块表
model Category {
  id          String   @id @default(cuid())
  name        String
  description String?
  slug        String   @unique
  color       String?
  icon        String?
  order       Int      @default(0)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  posts       Post[]
  
  @@map("categories")
}

// 帖子表
model Post {
  id          String   @id @default(cuid())
  title       String
  content     String
  excerpt     String?
  slug        String   @unique
  authorId    String
  categoryId  String
  isPublished Boolean  @default(true)
  isPinned    Boolean  @default(false)
  isLocked    Boolean  @default(false)
  views       Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  author      User     @relation(fields: [authorId], references: [id], onDelete: Cascade)
  category    Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  comments    Comment[]
  likes       Like[]
  
  @@map("posts")
}

// 评论表
model Comment {
  id          String   @id @default(cuid())
  content     String
  authorId    String
  postId      String
  parentId    String?
  isApproved  Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  author      User     @relation(fields: [authorId], references: [id], onDelete: Cascade)
  post        Post     @relation(fields: [postId], references: [id], onDelete: Cascade)
  parent      Comment? @relation("CommentReplies", fields: [parentId], references: [id])
  replies     Comment[] @relation("CommentReplies")
  likes       Like[]
  
  @@map("comments")
}

// 点赞表
model Like {
  id        String   @id @default(cuid())
  userId    String
  postId    String?
  commentId String?
  createdAt DateTime @default(now())

  // 关联关系
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  post      Post?    @relation(fields: [postId], references: [id], onDelete: Cascade)
  comment   Comment? @relation(fields: [commentId], references: [id], onDelete: Cascade)

  @@unique([userId, postId])
  @@unique([userId, commentId])
  @@map("likes")
}

// 系统日志表
model SystemLog {
  id        String   @id @default(cuid())
  level     LogLevel
  message   String
  details   String?
  userId    String?
  ip        String?
  userAgent String?
  createdAt DateTime @default(now())
  
  @@map("system_logs")
}

// 日志级别枚举
enum LogLevel {
  INFO
  WARN
  ERROR
  DEBUG
}

// 网站设置表
model Setting {
  id        String   @id @default(cuid())
  key       String   @unique
  value     String
  type      String   @default("string")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("settings")
}
