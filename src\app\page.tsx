import { Hero } from '@/components/sections/hero';
import { Features } from '@/components/sections/features';
import { RecentPosts } from '@/components/sections/recent-posts';
import { RecentAnnouncements } from '@/components/sections/recent-announcements';
import { CommunityStats } from '@/components/sections/community-stats';

export default function HomePage() {
  return (
    <div className="space-y-16">
      {/* Hero Section */}
      <Hero />
      
      {/* Community Stats */}
      <section className="container">
        <CommunityStats />
      </section>
      
      {/* Recent Announcements */}
      <section className="container">
        <RecentAnnouncements />
      </section>
      
      {/* Recent Posts */}
      <section className="container">
        <RecentPosts />
      </section>
      
      {/* Features */}
      <section className="container">
        <Features />
      </section>
    </div>
  );
}
