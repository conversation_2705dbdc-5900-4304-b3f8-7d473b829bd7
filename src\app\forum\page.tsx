import { Metadata } from 'next';
import { ForumCategories } from '@/components/forum/forum-categories';
import { RecentPosts } from '@/components/forum/recent-posts';
import { ForumStats } from '@/components/forum/forum-stats';

export const metadata: Metadata = {
  title: 'Forum - Community Platform',
  description: 'Join discussions, share ideas, and connect with our community members.',
};

export default function ForumPage() {
  return (
    <div className="container py-8">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold tracking-tight mb-2">
            Community Forum
          </h1>
          <p className="text-lg text-muted-foreground">
            Join discussions, share ideas, and connect with community members from around the world.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            <ForumCategories />
            <RecentPosts />
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            <ForumStats />
          </div>
        </div>
      </div>
    </div>
  );
}
