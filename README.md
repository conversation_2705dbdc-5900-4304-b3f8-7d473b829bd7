# Community Platform

A comprehensive, modern community platform built with Next.js, featuring file uploads, announcements, forums, and admin management. Designed to support international users with multi-language support and scalable architecture.

## 🌟 Features

### Core Features
- **User Authentication**: Registration, login, profile management with custom avatars
- **Forum System**: Threaded discussions with categories, posts, comments, and likes
- **File Sharing**: Upload and share images, videos, documents with organized storage
- **Announcements**: Rich text announcements with pinning and publishing controls
- **Admin Dashboard**: Comprehensive management interface for users, content, and system monitoring

### Advanced Features
- **Multi-language Support**: 8 languages (EN, ZH, JA, KO, ES, FR, DE, RU)
- **Theme System**: Light/Dark mode with smooth transitions
- **Real-time Updates**: Live notifications and activity feeds
- **Performance Monitoring**: System logs, analytics, and health monitoring
- **Responsive Design**: Mobile-first design that works on all devices
- **SEO Optimized**: Meta tags, OpenGraph, and search engine friendly

## 🚀 Tech Stack

- **Frontend**: Next.js 14, React 18, TypeScript
- **Styling**: Tailwind CSS, Radix UI, Lucide Icons
- **Database**: SQLite (development), PostgreSQL (production ready)
- **ORM**: Prisma
- **Authentication**: NextAuth.js
- **File Upload**: Multer
- **State Management**: Zustand
- **Form Handling**: React Hook Form + Zod validation

## 📦 Installation

### Prerequisites
- Node.js 18+
- npm or yarn or pnpm

### Quick Start

1. **Download Node.js**
   - Visit [nodejs.org](https://nodejs.org/) and download the latest LTS version
   - Install Node.js (this will also install npm)

2. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd community-platform
   ```

3. **Run the test setup script**
   ```bash
   node test-setup.js
   ```
   This will check your environment and guide you through any missing requirements.

4. **Install dependencies**
   ```bash
   npm install
   ```

5. **Environment Setup**
   ```bash
   cp .env.example .env.local
   ```
   The default configuration will work for development.

6. **Database Setup**
   ```bash
   # Generate Prisma client
   npx prisma generate

   # Initialize database
   npx prisma db push
   ```

7. **Start Development Server**
   ```bash
   npm run dev
   ```

8. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### First Time Setup

After starting the server:

1. **Create Admin Account**
   - Visit `/admin` to access the admin panel
   - Use the credentials from your `.env.local` file
   - Default: admin / admin123

2. **Explore Features**
   - Browse the forum categories
   - Upload some test files
   - Create announcements
   - Test the theme switcher (light/dark mode)
   - Try different languages

3. **Customize Your Platform**
   - Update site name and description in `.env.local`
   - Add your own categories in the admin panel
   - Configure SMTP for email notifications (optional)

## 🗂️ Project Structure

```
community-platform/
├── src/
│   ├── app/                 # Next.js App Router
│   ├── components/          # React components
│   │   ├── ui/             # Base UI components
│   │   ├── layout/         # Layout components
│   │   └── sections/       # Page sections
│   ├── lib/                # Utility libraries
│   ├── types/              # TypeScript type definitions
│   ├── utils/              # Helper functions
│   └── styles/             # Global styles
├── prisma/                 # Database schema and migrations
├── public/                 # Static assets
│   ├── uploads/           # User uploaded files
│   └── avatars/           # User avatars
└── ...config files
```

## 🔧 Configuration

### Database
- Development: SQLite (included)
- Production: PostgreSQL recommended
- Configure `DATABASE_URL` in `.env.local`

### File Uploads
- Default max size: 50MB
- Supported formats: Images, Videos, Documents
- Storage: Local filesystem (configurable)

### Authentication
- Email/Password authentication
- Role-based access control (User, Moderator, Admin, Super Admin)
- Session management with NextAuth.js

## 🌍 Multi-language Support

Supported languages:
- English (en)
- Chinese (zh)
- Japanese (ja)
- Korean (ko)
- Spanish (es)
- French (fr)
- German (de)
- Russian (ru)

## 🎨 Theming

- **Light Mode**: Clean, professional appearance
- **Dark Mode**: Easy on the eyes for extended use
- **System**: Automatically follows OS preference
- **Customizable**: Easy to modify colors and styling

## 👥 User Roles

1. **User**: Basic access to forum, file uploads, profile management
2. **Moderator**: Content moderation, user management
3. **Admin**: Full system access, user management, content control
4. **Super Admin**: Complete system control, settings management

## 📊 Performance

- **Scalability**: Designed for 100+ concurrent users
- **Optimization**: Image optimization, lazy loading, caching
- **Monitoring**: Built-in performance tracking and logging
- **SEO**: Optimized for search engines

## 🛡️ Security

- **Password Hashing**: bcrypt with salt rounds
- **Input Validation**: Zod schema validation
- **Rate Limiting**: API endpoint protection
- **File Upload Security**: Type and size validation
- **XSS Protection**: Sanitized user inputs

## 🚀 Deployment

### Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Configure environment variables
3. Deploy automatically

### Docker
```bash
# Build image
docker build -t community-platform .

# Run container
docker run -p 3000:3000 community-platform
```

### Manual Deployment
1. Build the application: `npm run build`
2. Start production server: `npm start`
3. Configure reverse proxy (nginx recommended)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the `/docs` folder for detailed guides
- **Issues**: Report bugs via GitHub Issues
- **Discussions**: Use GitHub Discussions for questions
- **Email**: <EMAIL>

## 🎯 Roadmap

- [ ] Real-time chat system
- [ ] Mobile app (React Native)
- [ ] Advanced search functionality
- [ ] Plugin system for extensions
- [ ] API documentation with Swagger
- [ ] Advanced analytics dashboard
- [ ] Email notifications
- [ ] Social media integration

---

**Built with ❤️ for the global community**
