export const locales = ['en', 'zh', 'ja', 'ko', 'es', 'fr', 'de', 'ru'] as const;
export type Locale = typeof locales[number];

export const defaultLocale: Locale = 'en';

export const localeNames: Record<Locale, string> = {
  en: 'English',
  zh: '中文',
  ja: '日本語',
  ko: '한국어',
  es: 'Español',
  fr: 'Français',
  de: 'Deutsch',
  ru: 'Русский',
};

export const localeFlags: Record<Locale, string> = {
  en: '🇺🇸',
  zh: '🇨🇳',
  ja: '🇯🇵',
  ko: '🇰🇷',
  es: '🇪🇸',
  fr: '🇫🇷',
  de: '🇩🇪',
  ru: '🇷🇺',
};

// Translation keys and default values
export const translations = {
  en: {
    // Navigation
    'nav.home': 'Home',
    'nav.forum': 'Forum',
    'nav.files': 'Files',
    'nav.announcements': 'Announcements',
    'nav.login': 'Login',
    'nav.signup': 'Sign Up',
    'nav.profile': 'Profile',
    'nav.admin': 'Admin',
    'nav.logout': 'Logout',

    // Common
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.success': 'Success',
    'common.cancel': 'Cancel',
    'common.save': 'Save',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.view': 'View',
    'common.search': 'Search',
    'common.filter': 'Filter',
    'common.sort': 'Sort',
    'common.upload': 'Upload',
    'common.download': 'Download',
    'common.share': 'Share',
    'common.like': 'Like',
    'common.comment': 'Comment',
    'common.reply': 'Reply',
    'common.submit': 'Submit',
    'common.back': 'Back',
    'common.next': 'Next',
    'common.previous': 'Previous',
    'common.close': 'Close',

    // Home page
    'home.hero.title': 'Connect, Share, Grow Together',
    'home.hero.subtitle': 'Join our vibrant community platform where people from around the world connect, share files, discuss ideas, and stay updated with the latest announcements.',
    'home.hero.getStarted': 'Get Started',
    'home.hero.exploreForum': 'Explore Forum',
    'home.stats.activeUsers': 'Active Users',
    'home.stats.discussions': 'Discussions',
    'home.stats.filesShared': 'Files Shared',
    'home.stats.support': 'Support',

    // Forum
    'forum.title': 'Community Forum',
    'forum.subtitle': 'Join discussions, share ideas, and connect with community members from around the world.',
    'forum.categories': 'Categories',
    'forum.recentPosts': 'Recent Discussions',
    'forum.createPost': 'Create Post',
    'forum.noPosts': 'No posts yet',
    'forum.beFirst': 'Be the first to start a discussion!',

    // Files
    'files.title': 'File Sharing',
    'files.subtitle': 'Upload and share files with the community. Support for images, videos, documents, and more.',
    'files.browse': 'Browse Files',
    'files.upload': 'Upload Files',
    'files.noFiles': 'No files found',
    'files.adjustSearch': 'Try adjusting your search or filter criteria',

    // Announcements
    'announcements.title': 'Announcements',
    'announcements.subtitle': 'Stay updated with the latest news and important information from our community.',
    'announcements.noAnnouncements': 'No announcements found',
    'announcements.checkLater': 'Check back later for updates',

    // User
    'user.profile': 'Profile',
    'user.settings': 'Settings',
    'user.posts': 'Posts',
    'user.comments': 'Comments',
    'user.files': 'Files',
    'user.joinDate': 'Joined',
    'user.lastSeen': 'Last seen',
    'user.bio': 'Bio',
    'user.location': 'Location',
    'user.website': 'Website',

    // Admin
    'admin.dashboard': 'Dashboard',
    'admin.users': 'Users',
    'admin.posts': 'Posts & Comments',
    'admin.files': 'Files',
    'admin.announcements': 'Announcements',
    'admin.categories': 'Categories',
    'admin.analytics': 'Analytics',
    'admin.logs': 'System Logs',
    'admin.settings': 'Settings',
    'admin.backToSite': 'Back to Site',

    // Time
    'time.justNow': 'Just now',
    'time.minutesAgo': 'minutes ago',
    'time.hoursAgo': 'hours ago',
    'time.daysAgo': 'days ago',
    'time.weeksAgo': 'weeks ago',
    'time.monthsAgo': 'months ago',
    'time.yearsAgo': 'years ago',

    // Errors
    'error.notFound': 'Page not found',
    'error.serverError': 'Internal server error',
    'error.unauthorized': 'Unauthorized access',
    'error.forbidden': 'Access forbidden',
    'error.networkError': 'Network error',
    'error.tryAgain': 'Please try again',

    // Success messages
    'success.saved': 'Successfully saved',
    'success.deleted': 'Successfully deleted',
    'success.uploaded': 'Successfully uploaded',
    'success.updated': 'Successfully updated',
    'success.created': 'Successfully created',
  },
  zh: {
    // Navigation
    'nav.home': '首页',
    'nav.forum': '论坛',
    'nav.files': '文件',
    'nav.announcements': '公告',
    'nav.login': '登录',
    'nav.signup': '注册',
    'nav.profile': '个人资料',
    'nav.admin': '管理',
    'nav.logout': '退出',

    // Common
    'common.loading': '加载中...',
    'common.error': '错误',
    'common.success': '成功',
    'common.cancel': '取消',
    'common.save': '保存',
    'common.delete': '删除',
    'common.edit': '编辑',
    'common.view': '查看',
    'common.search': '搜索',
    'common.filter': '筛选',
    'common.sort': '排序',
    'common.upload': '上传',
    'common.download': '下载',
    'common.share': '分享',
    'common.like': '点赞',
    'common.comment': '评论',
    'common.reply': '回复',
    'common.submit': '提交',
    'common.back': '返回',
    'common.next': '下一页',
    'common.previous': '上一页',
    'common.close': '关闭',

    // Home page
    'home.hero.title': '连接、分享、共同成长',
    'home.hero.subtitle': '加入我们充满活力的社区平台，来自世界各地的人们在这里连接、分享文件、讨论想法，并获取最新公告。',
    'home.hero.getStarted': '开始使用',
    'home.hero.exploreForum': '探索论坛',
    'home.stats.activeUsers': '活跃用户',
    'home.stats.discussions': '讨论',
    'home.stats.filesShared': '共享文件',
    'home.stats.support': '支持',

    // Forum
    'forum.title': '社区论坛',
    'forum.subtitle': '参与讨论，分享想法，与来自世界各地的社区成员建立联系。',
    'forum.categories': '分类',
    'forum.recentPosts': '最新讨论',
    'forum.createPost': '创建帖子',
    'forum.noPosts': '暂无帖子',
    'forum.beFirst': '成为第一个开始讨论的人！',

    // Files
    'files.title': '文件分享',
    'files.subtitle': '与社区分享文件。支持图片、视频、文档等多种格式。',
    'files.browse': '浏览文件',
    'files.upload': '上传文件',
    'files.noFiles': '未找到文件',
    'files.adjustSearch': '请尝试调整搜索或筛选条件',

    // Announcements
    'announcements.title': '公告',
    'announcements.subtitle': '获取社区最新消息和重要信息。',
    'announcements.noAnnouncements': '未找到公告',
    'announcements.checkLater': '请稍后查看更新',

    // Time
    'time.justNow': '刚刚',
    'time.minutesAgo': '分钟前',
    'time.hoursAgo': '小时前',
    'time.daysAgo': '天前',
    'time.weeksAgo': '周前',
    'time.monthsAgo': '月前',
    'time.yearsAgo': '年前',
  },
  // Add more languages as needed...
} as const;

export function getTranslation(locale: Locale, key: string): string {
  const localeTranslations = translations[locale] || translations[defaultLocale];
  return (localeTranslations as any)[key] || key;
}

export function formatRelativeTime(date: Date | string, locale: Locale = defaultLocale): string {
  const d = new Date(date);
  const now = new Date();
  const diff = now.getTime() - d.getTime();
  
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  const weeks = Math.floor(days / 7);
  const months = Math.floor(days / 30);
  const years = Math.floor(days / 365);

  if (years > 0) return `${years} ${getTranslation(locale, 'time.yearsAgo')}`;
  if (months > 0) return `${months} ${getTranslation(locale, 'time.monthsAgo')}`;
  if (weeks > 0) return `${weeks} ${getTranslation(locale, 'time.weeksAgo')}`;
  if (days > 0) return `${days} ${getTranslation(locale, 'time.daysAgo')}`;
  if (hours > 0) return `${hours} ${getTranslation(locale, 'time.hoursAgo')}`;
  if (minutes > 0) return `${minutes} ${getTranslation(locale, 'time.minutesAgo')}`;
  return getTranslation(locale, 'time.justNow');
}
