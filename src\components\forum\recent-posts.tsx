"use client";

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MessageSquare, Heart, Eye, Pin, ArrowRight } from 'lucide-react';
import { formatRelativeTime } from '@/lib/utils';

interface Post {
  id: string;
  title: string;
  excerpt?: string;
  slug: string;
  isPinned: boolean;
  views: number;
  createdAt: string;
  author: {
    id: string;
    username: string;
    avatar?: string;
  };
  category: {
    id: string;
    name: string;
    slug: string;
    color?: string;
  };
  _count: {
    comments: number;
    likes: number;
  };
}

export function RecentPosts() {
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPosts = async () => {
      try {
        const response = await fetch('/api/forum/posts?limit=10');
        const result = await response.json();
        
        if (result.success) {
          setPosts(result.data);
        }
      } catch (error) {
        console.error('Error fetching posts:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPosts();
  }, []);

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse space-y-3">
                <div className="h-4 bg-muted rounded w-3/4"></div>
                <div className="h-3 bg-muted rounded w-full"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold tracking-tight">Recent Discussions</h2>
        <Button variant="outline" asChild>
          <Link href="/forum/posts">
            View All Posts
            <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </div>

      <div className="space-y-4">
        {posts.map((post) => (
          <Card key={post.id} className="card-hover">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-2 mb-2">
                  {post.isPinned && (
                    <Pin className="h-4 w-4 text-yellow-500" />
                  )}
                  <Badge 
                    variant="secondary"
                    style={{ 
                      backgroundColor: `${post.category.color || '#3B82F6'}20`, 
                      color: post.category.color || '#3B82F6' 
                    }}
                  >
                    {post.category.name}
                  </Badge>
                </div>
                <span className="text-sm text-muted-foreground">
                  {formatRelativeTime(post.createdAt)}
                </span>
              </div>
              <CardTitle className="text-xl hover:text-primary transition-colors">
                <Link href={`/forum/post/${post.slug}`}>
                  {post.title}
                </Link>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {post.excerpt && (
                <CardDescription className="text-base leading-relaxed mb-4">
                  {post.excerpt}
                </CardDescription>
              )}
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <span>By {post.author.username}</span>
                </div>
                
                <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <Eye className="h-4 w-4" />
                    <span>{post.views}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <MessageSquare className="h-4 w-4" />
                    <span>{post._count.comments}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Heart className="h-4 w-4" />
                    <span>{post._count.likes}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {posts.length === 0 && (
        <div className="text-center py-12">
          <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">No posts yet</h3>
          <p className="text-muted-foreground">
            Be the first to start a discussion!
          </p>
          <Button className="mt-4" asChild>
            <Link href="/forum/new-post">
              Create Post
            </Link>
          </Button>
        </div>
      )}
    </div>
  );
}
