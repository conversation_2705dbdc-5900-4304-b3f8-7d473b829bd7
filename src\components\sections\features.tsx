import { 
  MessageSquare, 
  Upload, 
  Bell, 
  Shield, 
  Globe, 
  Zap,
  Users,
  Settings,
  BarChart3
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export function Features() {
  const features = [
    {
      icon: MessageSquare,
      title: "Dynamic Forum",
      description: "Engage in meaningful discussions with threaded conversations, real-time updates, and advanced moderation tools.",
      color: "text-blue-500"
    },
    {
      icon: Upload,
      title: "File Sharing",
      description: "Upload and share files securely with support for images, videos, documents, and more. Organized storage with easy access.",
      color: "text-green-500"
    },
    {
      icon: Bell,
      title: "Announcements",
      description: "Stay informed with important updates, news, and announcements. Rich text editor with multimedia support.",
      color: "text-yellow-500"
    },
    {
      icon: Shield,
      title: "Admin Dashboard",
      description: "Comprehensive admin panel for managing users, content, and system settings. Real-time monitoring and analytics.",
      color: "text-red-500"
    },
    {
      icon: Globe,
      title: "Multi-language",
      description: "Support for 8 languages including English, Chinese, Japanese, Korean, Spanish, French, German, and Russian.",
      color: "text-purple-500"
    },
    {
      icon: Zap,
      title: "High Performance",
      description: "Built for scale with optimized performance, caching, and support for 100+ concurrent users.",
      color: "text-orange-500"
    },
    {
      icon: Users,
      title: "User Management",
      description: "Robust user system with profiles, avatars, roles, and permissions. Social features and activity tracking.",
      color: "text-cyan-500"
    },
    {
      icon: Settings,
      title: "Customizable",
      description: "Flexible theming system with light/dark modes, customizable layouts, and personalization options.",
      color: "text-pink-500"
    },
    {
      icon: BarChart3,
      title: "Analytics",
      description: "Detailed analytics and reporting for user activity, content performance, and system health monitoring.",
      color: "text-indigo-500"
    }
  ];

  return (
    <section className="py-16">
      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
          Powerful Features for Modern Communities
        </h2>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Everything you need to build and manage a thriving online community. 
          From discussions to file sharing, we've got you covered.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {features.map((feature, index) => {
          const Icon = feature.icon;
          return (
            <Card key={index} className="card-hover">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg bg-secondary ${feature.color}`}>
                    <Icon className="h-6 w-6" />
                  </div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base leading-relaxed">
                  {feature.description}
                </CardDescription>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </section>
  );
}
