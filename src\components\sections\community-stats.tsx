"use client";

import { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Users, MessageSquare, Upload, TrendingUp } from "lucide-react";

export function CommunityStats() {
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalPosts: 0,
    totalFiles: 0,
    activeUsers: 0
  });

  // 模拟数据加载
  useEffect(() => {
    const timer = setTimeout(() => {
      setStats({
        totalUsers: 12547,
        totalPosts: 8932,
        totalFiles: 15678,
        activeUsers: 1234
      });
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  const statItems = [
    {
      title: "Total Users",
      value: stats.totalUsers.toLocaleString(),
      icon: Users,
      description: "Registered members",
      trend: "+12% this month"
    },
    {
      title: "Discussions",
      value: stats.totalPosts.toLocaleString(),
      icon: MessageSquare,
      description: "Forum posts & replies",
      trend: "+8% this week"
    },
    {
      title: "Files Shared",
      value: stats.totalFiles.toLocaleString(),
      icon: Upload,
      description: "Total uploads",
      trend: "+15% this month"
    },
    {
      title: "Active Now",
      value: stats.activeUsers.toLocaleString(),
      icon: TrendingUp,
      description: "Online users",
      trend: "Live count"
    }
  ];

  return (
    <section className="py-12">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold tracking-tight sm:text-3xl mb-2">
          Community at a Glance
        </h2>
        <p className="text-muted-foreground">
          Real-time statistics from our growing community
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {statItems.map((item, index) => {
          const Icon = item.icon;
          return (
            <Card key={index} className="text-center">
              <CardHeader className="pb-3">
                <div className="flex justify-center mb-2">
                  <div className="p-3 rounded-full bg-primary/10">
                    <Icon className="h-6 w-6 text-primary" />
                  </div>
                </div>
                <CardTitle className="text-2xl font-bold">
                  {item.value}
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-sm font-medium mb-1">{item.title}</p>
                <p className="text-xs text-muted-foreground mb-2">
                  {item.description}
                </p>
                <div className="text-xs text-green-600 font-medium">
                  {item.trend}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </section>
  );
}
