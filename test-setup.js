#!/usr/bin/env node

/**
 * Community Platform Test Setup Script
 * 
 * This script tests the basic functionality of the community platform
 * and helps verify that everything is working correctly.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Community Platform Test Setup');
console.log('================================\n');

// Color codes for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function success(message) {
  log(`✅ ${message}`, 'green');
}

function error(message) {
  log(`❌ ${message}`, 'red');
}

function warning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function info(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Test functions
function checkNodeVersion() {
  info('Checking Node.js version...');
  try {
    const version = execSync('node --version', { encoding: 'utf8' }).trim();
    const majorVersion = parseInt(version.slice(1).split('.')[0]);
    
    if (majorVersion >= 18) {
      success(`Node.js version: ${version}`);
      return true;
    } else {
      error(`Node.js version ${version} is too old. Please upgrade to Node.js 18+`);
      return false;
    }
  } catch (err) {
    error('Node.js is not installed');
    return false;
  }
}

function checkPackageJson() {
  info('Checking package.json...');
  try {
    const packagePath = path.join(process.cwd(), 'package.json');
    if (fs.existsSync(packagePath)) {
      const pkg = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      success(`Project: ${pkg.name} v${pkg.version}`);
      return true;
    } else {
      error('package.json not found');
      return false;
    }
  } catch (err) {
    error('Error reading package.json');
    return false;
  }
}

function checkEnvironmentFile() {
  info('Checking environment configuration...');
  const envPath = path.join(process.cwd(), '.env.local');
  const envExamplePath = path.join(process.cwd(), '.env.example');
  
  if (fs.existsSync(envPath)) {
    success('.env.local file exists');
    return true;
  } else if (fs.existsSync(envExamplePath)) {
    warning('.env.local not found, but .env.example exists');
    info('Please copy .env.example to .env.local and configure your settings');
    return false;
  } else {
    error('No environment configuration files found');
    return false;
  }
}

function checkDependencies() {
  info('Checking dependencies...');
  try {
    const nodeModulesPath = path.join(process.cwd(), 'node_modules');
    if (fs.existsSync(nodeModulesPath)) {
      success('Dependencies are installed');
      return true;
    } else {
      warning('Dependencies not installed');
      info('Run: npm install');
      return false;
    }
  } catch (err) {
    error('Error checking dependencies');
    return false;
  }
}

function checkPrismaSetup() {
  info('Checking Prisma setup...');
  try {
    const schemaPath = path.join(process.cwd(), 'prisma', 'schema.prisma');
    if (fs.existsSync(schemaPath)) {
      success('Prisma schema found');
      
      // Check if Prisma client is generated
      const clientPath = path.join(process.cwd(), 'node_modules', '.prisma', 'client');
      if (fs.existsSync(clientPath)) {
        success('Prisma client is generated');
        return true;
      } else {
        warning('Prisma client not generated');
        info('Run: npx prisma generate');
        return false;
      }
    } else {
      error('Prisma schema not found');
      return false;
    }
  } catch (err) {
    error('Error checking Prisma setup');
    return false;
  }
}

function checkDirectoryStructure() {
  info('Checking directory structure...');
  const requiredDirs = [
    'src',
    'src/app',
    'src/components',
    'src/lib',
    'src/types',
    'src/utils',
    'src/styles',
    'public',
    'public/uploads',
    'public/avatars',
    'prisma'
  ];
  
  let allExist = true;
  
  requiredDirs.forEach(dir => {
    const dirPath = path.join(process.cwd(), dir);
    if (fs.existsSync(dirPath)) {
      success(`Directory exists: ${dir}`);
    } else {
      error(`Directory missing: ${dir}`);
      allExist = false;
    }
  });
  
  return allExist;
}

function checkConfigFiles() {
  info('Checking configuration files...');
  const configFiles = [
    'next.config.js',
    'tailwind.config.js',
    'tsconfig.json',
    'package.json'
  ];
  
  let allExist = true;
  
  configFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file);
    if (fs.existsSync(filePath)) {
      success(`Config file exists: ${file}`);
    } else {
      error(`Config file missing: ${file}`);
      allExist = false;
    }
  });
  
  return allExist;
}

function runBuildTest() {
  info('Testing build process...');
  try {
    execSync('npm run build', { stdio: 'pipe' });
    success('Build completed successfully');
    return true;
  } catch (err) {
    error('Build failed');
    console.log(err.stdout?.toString());
    console.log(err.stderr?.toString());
    return false;
  }
}

function checkDatabaseConnection() {
  info('Checking database connection...');
  try {
    // This would require the app to be running
    // For now, just check if the database file exists (for SQLite)
    const dbPath = path.join(process.cwd(), 'prisma', 'dev.db');
    if (fs.existsSync(dbPath)) {
      success('Database file exists');
      return true;
    } else {
      warning('Database not initialized');
      info('Run: npx prisma db push');
      return false;
    }
  } catch (err) {
    error('Error checking database');
    return false;
  }
}

function generateTestReport(results) {
  console.log('\n' + '='.repeat(50));
  log('📊 TEST REPORT', 'bold');
  console.log('='.repeat(50));
  
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  results.forEach(result => {
    if (result.passed) {
      success(`${result.name}: PASSED`);
    } else {
      error(`${result.name}: FAILED`);
    }
  });
  
  console.log('\n' + '-'.repeat(50));
  
  if (passed === total) {
    success(`All tests passed! (${passed}/${total})`);
    console.log('\n🎉 Your Community Platform is ready to go!');
    console.log('\nNext steps:');
    console.log('1. Start the development server: npm run dev');
    console.log('2. Open http://localhost:3000 in your browser');
    console.log('3. Create your first admin user');
    console.log('4. Explore the platform features');
  } else {
    warning(`${passed}/${total} tests passed`);
    console.log('\n🔧 Please fix the failed tests before proceeding.');
    console.log('\nFor help, check:');
    console.log('- README.md for setup instructions');
    console.log('- DEPLOYMENT.md for deployment guide');
    console.log('- .env.example for configuration options');
  }
  
  console.log('\n📚 Documentation:');
  console.log('- README.md - Setup and usage guide');
  console.log('- DEPLOYMENT.md - Production deployment');
  console.log('- src/types/index.ts - Type definitions');
  
  console.log('\n🆘 Need help?');
  console.log('- Check the GitHub repository for issues');
  console.log('- Review the application logs');
  console.log('- Contact support team');
}

// Main test execution
async function runTests() {
  const tests = [
    { name: 'Node.js Version', test: checkNodeVersion },
    { name: 'Package.json', test: checkPackageJson },
    { name: 'Environment Config', test: checkEnvironmentFile },
    { name: 'Dependencies', test: checkDependencies },
    { name: 'Directory Structure', test: checkDirectoryStructure },
    { name: 'Config Files', test: checkConfigFiles },
    { name: 'Prisma Setup', test: checkPrismaSetup },
    { name: 'Database Connection', test: checkDatabaseConnection },
  ];
  
  const results = [];
  
  for (const test of tests) {
    try {
      const passed = test.test();
      results.push({ name: test.name, passed });
    } catch (err) {
      error(`${test.name}: ERROR - ${err.message}`);
      results.push({ name: test.name, passed: false });
    }
    console.log(''); // Add spacing between tests
  }
  
  generateTestReport(results);
}

// Run the tests
runTests().catch(err => {
  error(`Test execution failed: ${err.message}`);
  process.exit(1);
});
