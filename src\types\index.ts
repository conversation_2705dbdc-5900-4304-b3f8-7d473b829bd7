// 用户相关类型
export interface User {
  id: string;
  email: string;
  username: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  bio?: string;
  role: Role;
  isActive: boolean;
  language: string;
  theme: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum Role {
  USER = 'USER',
  MODERATOR = 'MODERATOR',
  ADMIN = 'ADMIN',
  SUPER_ADMIN = 'SUPER_ADMIN'
}

// 文件相关类型
export interface FileUpload {
  id: string;
  filename: string;
  originalName: string;
  mimetype: string;
  size: number;
  path: string;
  url: string;
  uploadedBy: string;
  description?: string;
  isPublic: boolean;
  downloads: number;
  createdAt: Date;
  updatedAt: Date;
  user: User;
}

// 公告相关类型
export interface Announcement {
  id: string;
  title: string;
  content: string;
  excerpt?: string;
  isPublished: boolean;
  isPinned: boolean;
  authorId: string;
  views: number;
  createdAt: Date;
  updatedAt: Date;
  author: User;
}

// 论坛相关类型
export interface Category {
  id: string;
  name: string;
  description?: string;
  slug: string;
  color?: string;
  icon?: string;
  order: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  posts: Post[];
}

export interface Post {
  id: string;
  title: string;
  content: string;
  excerpt?: string;
  slug: string;
  authorId: string;
  categoryId: string;
  isPublished: boolean;
  isPinned: boolean;
  isLocked: boolean;
  views: number;
  createdAt: Date;
  updatedAt: Date;
  author: User;
  category: Category;
  comments: Comment[];
  likes: Like[];
  _count?: {
    comments: number;
    likes: number;
  };
}

export interface Comment {
  id: string;
  content: string;
  authorId: string;
  postId: string;
  parentId?: string;
  isApproved: boolean;
  createdAt: Date;
  updatedAt: Date;
  author: User;
  post: Post;
  parent?: Comment;
  replies: Comment[];
  likes: Like[];
  _count?: {
    likes: number;
    replies: number;
  };
}

export interface Like {
  id: string;
  userId: string;
  postId?: string;
  commentId?: string;
  createdAt: Date;
  user: User;
}

// 系统日志类型
export interface SystemLog {
  id: string;
  level: LogLevel;
  message: string;
  details?: string;
  userId?: string;
  ip?: string;
  userAgent?: string;
  createdAt: Date;
}

export enum LogLevel {
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
  DEBUG = 'DEBUG'
}

// 设置类型
export interface Setting {
  id: string;
  key: string;
  value: string;
  type: string;
  createdAt: Date;
  updatedAt: Date;
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// 分页类型
export interface PaginationParams {
  page: number;
  limit: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// 表单类型
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  email: string;
  username: string;
  password: string;
  confirmPassword: string;
  firstName?: string;
  lastName?: string;
}

export interface ProfileForm {
  firstName?: string;
  lastName?: string;
  bio?: string;
  language: string;
  theme: string;
}

export interface PostForm {
  title: string;
  content: string;
  categoryId: string;
  isPublished: boolean;
  isPinned: boolean;
}

export interface AnnouncementForm {
  title: string;
  content: string;
  isPublished: boolean;
  isPinned: boolean;
}

// 主题类型
export type Theme = 'light' | 'dark';

// 语言类型
export type Language = 'en' | 'zh' | 'ja' | 'ko' | 'es' | 'fr' | 'de' | 'ru';

// 导航项类型
export interface NavItem {
  title: string;
  href: string;
  icon?: string;
  children?: NavItem[];
}

// 统计数据类型
export interface DashboardStats {
  totalUsers: number;
  totalPosts: number;
  totalComments: number;
  totalFiles: number;
  totalAnnouncements: number;
  activeUsers: number;
  recentActivity: {
    type: 'user' | 'post' | 'comment' | 'file' | 'announcement';
    title: string;
    user: string;
    createdAt: Date;
  }[];
}

// 文件上传配置
export interface UploadConfig {
  maxSize: number;
  allowedTypes: string[];
  uploadPath: string;
}

// 通知类型
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  duration?: number;
}
