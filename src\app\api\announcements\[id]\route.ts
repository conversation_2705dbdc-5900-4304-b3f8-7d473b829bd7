import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const announcement = await db.announcement.findUnique({
      where: { id: params.id },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            avatar: true,
          },
        },
      },
    });

    if (!announcement) {
      return NextResponse.json(
        { success: false, error: 'Announcement not found' },
        { status: 404 }
      );
    }

    // Increment view count
    await db.announcement.update({
      where: { id: params.id },
      data: { views: { increment: 1 } },
    });

    return NextResponse.json({
      success: true,
      data: { ...announcement, views: announcement.views + 1 },
    });

  } catch (error) {
    console.error('Announcement fetch error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const { title, content, isPublished, isPinned } = body;

    // TODO: Get user from session and check permissions
    const userId = 'temp-admin-id';

    if (!title || !content) {
      return NextResponse.json(
        { success: false, error: 'Title and content are required' },
        { status: 400 }
      );
    }

    // Check if announcement exists and user has permission
    const existingAnnouncement = await db.announcement.findUnique({
      where: { id: params.id },
    });

    if (!existingAnnouncement) {
      return NextResponse.json(
        { success: false, error: 'Announcement not found' },
        { status: 404 }
      );
    }

    // Generate excerpt from content
    const excerpt = content.replace(/<[^>]*>/g, '').substring(0, 200) + '...';

    const announcement = await db.announcement.update({
      where: { id: params.id },
      data: {
        title,
        content,
        excerpt,
        isPublished,
        isPinned,
      },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            avatar: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: announcement,
    });

  } catch (error) {
    console.error('Announcement update error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // TODO: Get user from session and check permissions
    const userId = 'temp-admin-id';

    // Check if announcement exists
    const existingAnnouncement = await db.announcement.findUnique({
      where: { id: params.id },
    });

    if (!existingAnnouncement) {
      return NextResponse.json(
        { success: false, error: 'Announcement not found' },
        { status: 404 }
      );
    }

    await db.announcement.delete({
      where: { id: params.id },
    });

    return NextResponse.json({
      success: true,
      message: 'Announcement deleted successfully',
    });

  } catch (error) {
    console.error('Announcement deletion error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
