"use client";

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Download, 
  Eye, 
  Search, 
  Filter,
  Image as ImageIcon,
  Video,
  FileText,
  File,
  Archive,
  User
} from 'lucide-react';
import { formatFileSize, formatRelativeTime, isImageFile, isVideoFile, isDocumentFile } from '@/lib/utils';

interface FileItem {
  id: string;
  filename: string;
  originalName: string;
  mimetype: string;
  size: number;
  url: string;
  description?: string;
  downloads: number;
  createdAt: string;
  user: {
    id: string;
    username: string;
    avatar?: string;
  };
}

interface FileListProps {
  initialFiles?: FileItem[];
}

export function FileList({ initialFiles = [] }: FileListProps) {
  const [files, setFiles] = useState<FileItem[]>(initialFiles);
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const getFileIcon = (mimetype: string) => {
    if (isImageFile(mimetype)) return <ImageIcon className="h-5 w-5 text-blue-500" />;
    if (isVideoFile(mimetype)) return <Video className="h-5 w-5 text-purple-500" />;
    if (isDocumentFile(mimetype)) return <FileText className="h-5 w-5 text-green-500" />;
    if (mimetype.includes('zip') || mimetype.includes('rar') || mimetype.includes('7z')) {
      return <Archive className="h-5 w-5 text-orange-500" />;
    }
    return <File className="h-5 w-5 text-gray-500" />;
  };

  const getFileTypeColor = (mimetype: string) => {
    if (isImageFile(mimetype)) return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
    if (isVideoFile(mimetype)) return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
    if (isDocumentFile(mimetype)) return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
    return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
  };

  const fetchFiles = async (reset = false) => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: reset ? '1' : page.toString(),
        limit: '20',
        search,
        type: typeFilter,
      });

      const response = await fetch(`/api/upload?${params}`);
      const result = await response.json();

      if (result.success) {
        if (reset) {
          setFiles(result.data);
          setPage(1);
        } else {
          setFiles(prev => [...prev, ...result.data]);
        }
        setHasMore(result.pagination.hasNext);
      }
    } catch (error) {
      console.error('Error fetching files:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    fetchFiles(true);
  };

  const handleDownload = async (file: FileItem) => {
    try {
      const response = await fetch(file.url);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = file.originalName;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Download error:', error);
    }
  };

  const loadMore = () => {
    setPage(prev => prev + 1);
    fetchFiles();
  };

  useEffect(() => {
    if (initialFiles.length === 0) {
      fetchFiles(true);
    }
  }, []);

  return (
    <div className="space-y-6">
      {/* Search and Filter */}
      <Card>
        <CardHeader>
          <CardTitle>File Library</CardTitle>
          <CardDescription>
            Browse and download shared files from the community
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search files..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="px-3 py-2 border border-input rounded-md bg-background text-foreground"
              >
                <option value="">All Types</option>
                <option value="image">Images</option>
                <option value="video">Videos</option>
                <option value="application">Documents</option>
                <option value="text">Text Files</option>
              </select>
              <Button onClick={handleSearch} variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* File Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {files.map((file) => (
          <Card key={file.id} className="card-hover">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {getFileIcon(file.mimetype)}
                  <Badge className={getFileTypeColor(file.mimetype)}>
                    {file.mimetype.split('/')[0]}
                  </Badge>
                </div>
                <span className="text-xs text-muted-foreground">
                  {formatFileSize(file.size)}
                </span>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* File Preview */}
              {isImageFile(file.mimetype) && (
                <div className="relative aspect-video rounded-lg overflow-hidden bg-muted">
                  <Image
                    src={file.url}
                    alt={file.originalName}
                    fill
                    className="object-cover"
                  />
                </div>
              )}

              {/* File Info */}
              <div>
                <h3 className="font-medium truncate" title={file.originalName}>
                  {file.originalName}
                </h3>
                {file.description && (
                  <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                    {file.description}
                  </p>
                )}
              </div>

              {/* User Info */}
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <User className="h-4 w-4" />
                <span>{file.user.username}</span>
                <span>•</span>
                <span>{formatRelativeTime(file.createdAt)}</span>
              </div>

              {/* Actions */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                  <Download className="h-4 w-4" />
                  <span>{file.downloads}</span>
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                  <Button 
                    size="sm" 
                    onClick={() => handleDownload(file)}
                  >
                    <Download className="h-4 w-4 mr-1" />
                    Download
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Load More */}
      {hasMore && (
        <div className="text-center">
          <Button 
            onClick={loadMore} 
            disabled={loading}
            variant="outline"
          >
            {loading ? 'Loading...' : 'Load More'}
          </Button>
        </div>
      )}

      {files.length === 0 && !loading && (
        <div className="text-center py-12">
          <File className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">No files found</h3>
          <p className="text-muted-foreground">
            Try adjusting your search or filter criteria
          </p>
        </div>
      )}
    </div>
  );
}
