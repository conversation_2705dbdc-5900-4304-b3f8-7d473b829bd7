"use client";

import Link from 'next/link';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Bell, Pin, Eye, Calendar, User, Share2 } from 'lucide-react';
import { formatDate, formatRelativeTime } from '@/lib/utils';

interface Announcement {
  id: string;
  title: string;
  content: string;
  excerpt?: string;
  isPublished: boolean;
  isPinned: boolean;
  views: number;
  createdAt: string;
  updatedAt: string;
  author: {
    id: string;
    username: string;
    avatar?: string;
  };
}

interface AnnouncementDetailProps {
  announcement: Announcement;
}

export function AnnouncementDetail({ announcement }: AnnouncementDetailProps) {
  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: announcement.title,
          text: announcement.excerpt || announcement.content.replace(/<[^>]*>/g, '').substring(0, 200),
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      try {
        await navigator.clipboard.writeText(window.location.href);
        alert('Link copied to clipboard!');
      } catch (error) {
        console.log('Error copying to clipboard:', error);
      }
    }
  };

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <Button variant="ghost" asChild>
        <Link href="/announcements">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Announcements
        </Link>
      </Button>

      {/* Announcement Content */}
      <Card>
        <CardHeader className="space-y-4">
          {/* Badges and Meta */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Bell className="h-5 w-5 text-primary" />
              {announcement.isPinned && (
                <Pin className="h-4 w-4 text-yellow-500" />
              )}
              <Badge variant={announcement.isPinned ? "default" : "secondary"}>
                {announcement.isPinned ? "Pinned Announcement" : "Announcement"}
              </Badge>
            </div>
            <Button variant="outline" size="sm" onClick={handleShare}>
              <Share2 className="h-4 w-4 mr-2" />
              Share
            </Button>
          </div>

          {/* Title */}
          <h1 className="text-3xl font-bold tracking-tight">
            {announcement.title}
          </h1>

          {/* Meta Information */}
          <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
            <div className="flex items-center space-x-1">
              <User className="h-4 w-4" />
              <span>By {announcement.author.username}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Calendar className="h-4 w-4" />
              <span>{formatDate(announcement.createdAt)}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Eye className="h-4 w-4" />
              <span>{announcement.views} views</span>
            </div>
            {announcement.updatedAt !== announcement.createdAt && (
              <div className="text-xs">
                Updated {formatRelativeTime(announcement.updatedAt)}
              </div>
            )}
          </div>
        </CardHeader>

        <CardContent>
          {/* Content */}
          <div 
            className="prose prose-gray dark:prose-invert max-w-none"
            dangerouslySetInnerHTML={{ __html: announcement.content }}
          />
        </CardContent>
      </Card>

      {/* Related Actions */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium mb-1">Stay Updated</h3>
              <p className="text-sm text-muted-foreground">
                Follow our announcements to never miss important updates.
              </p>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" asChild>
                <Link href="/announcements">
                  View All Announcements
                </Link>
              </Button>
              <Button asChild>
                <Link href="/forum">
                  Join Discussion
                </Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
