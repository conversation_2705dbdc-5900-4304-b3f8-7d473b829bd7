# Database
DATABASE_URL="file:./dev.db"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="development-secret-key-change-in-production"

# File Upload
UPLOAD_MAX_SIZE=50000000
UPLOAD_DIR="./public/uploads"
AVATAR_DIR="./public/avatars"

# Admin Settings
ADMIN_EMAIL="<EMAIL>"
ADMIN_USERNAME="admin"
ADMIN_PASSWORD="admin123"

# Application Settings
APP_NAME="Community Platform"
APP_URL="http://localhost:3000"
APP_DESCRIPTION="A comprehensive community platform"

# Security
JWT_SECRET="development-jwt-secret-change-in-production"
BCRYPT_ROUNDS=12

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=900000

# Logging
LOG_LEVEL="info"

# Performance
CACHE_TTL=3600
MAX_CONNECTIONS=100
