import { logger } from './logger';

interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  timestamp: Date;
  tags?: Record<string, string>;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private timers: Map<string, number> = new Map();

  // Start a timer
  startTimer(name: string): void {
    this.timers.set(name, Date.now());
  }

  // End a timer and record the metric
  endTimer(name: string, tags?: Record<string, string>): number {
    const startTime = this.timers.get(name);
    if (!startTime) {
      console.warn(`Timer '${name}' was not started`);
      return 0;
    }

    const duration = Date.now() - startTime;
    this.timers.delete(name);

    this.recordMetric({
      name,
      value: duration,
      unit: 'ms',
      timestamp: new Date(),
      tags,
    });

    // Log slow operations
    if (duration > 1000) {
      logger.warn(`Slow operation: ${name}`, `Duration: ${duration}ms`, {
        details: JSON.stringify(tags),
      });
    }

    return duration;
  }

  // Record a custom metric
  recordMetric(metric: PerformanceMetric): void {
    this.metrics.push(metric);

    // Keep only recent metrics (last 1000)
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }

    // Log performance metrics
    logger.logPerformance(
      metric.name,
      metric.value,
      JSON.stringify({ unit: metric.unit, tags: metric.tags })
    );
  }

  // Get metrics by name
  getMetrics(name?: string, limit: number = 100): PerformanceMetric[] {
    let filtered = this.metrics;
    
    if (name) {
      filtered = this.metrics.filter(m => m.name === name);
    }

    return filtered.slice(-limit);
  }

  // Get average for a metric
  getAverage(name: string, timeWindow?: number): number {
    let metrics = this.metrics.filter(m => m.name === name);

    if (timeWindow) {
      const cutoff = new Date(Date.now() - timeWindow);
      metrics = metrics.filter(m => m.timestamp > cutoff);
    }

    if (metrics.length === 0) return 0;

    const sum = metrics.reduce((acc, m) => acc + m.value, 0);
    return sum / metrics.length;
  }

  // Get percentile for a metric
  getPercentile(name: string, percentile: number, timeWindow?: number): number {
    let metrics = this.metrics.filter(m => m.name === name);

    if (timeWindow) {
      const cutoff = new Date(Date.now() - timeWindow);
      metrics = metrics.filter(m => m.timestamp > cutoff);
    }

    if (metrics.length === 0) return 0;

    const values = metrics.map(m => m.value).sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * values.length) - 1;
    return values[Math.max(0, index)];
  }

  // Clear old metrics
  clearOldMetrics(maxAge: number = 24 * 60 * 60 * 1000): void {
    const cutoff = new Date(Date.now() - maxAge);
    this.metrics = this.metrics.filter(m => m.timestamp > cutoff);
  }

  // Get system performance info
  getSystemInfo(): any {
    if (typeof window !== 'undefined') {
      // Browser environment
      return {
        userAgent: navigator.userAgent,
        language: navigator.language,
        platform: navigator.platform,
        cookieEnabled: navigator.cookieEnabled,
        onLine: navigator.onLine,
        memory: (performance as any).memory ? {
          usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
          totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
          jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit,
        } : undefined,
      };
    } else {
      // Node.js environment
      return {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        memory: process.memoryUsage(),
        uptime: process.uptime(),
        cpuUsage: process.cpuUsage(),
      };
    }
  }

  // Monitor database query performance
  async monitorQuery<T>(
    name: string,
    queryFn: () => Promise<T>,
    tags?: Record<string, string>
  ): Promise<T> {
    this.startTimer(`db_query_${name}`);
    
    try {
      const result = await queryFn();
      this.endTimer(`db_query_${name}`, { ...tags, status: 'success' });
      return result;
    } catch (error) {
      this.endTimer(`db_query_${name}`, { ...tags, status: 'error' });
      throw error;
    }
  }

  // Monitor API endpoint performance
  async monitorEndpoint<T>(
    endpoint: string,
    handler: () => Promise<T>,
    tags?: Record<string, string>
  ): Promise<T> {
    this.startTimer(`api_${endpoint}`);
    
    try {
      const result = await handler();
      this.endTimer(`api_${endpoint}`, { ...tags, status: 'success' });
      return result;
    } catch (error) {
      this.endTimer(`api_${endpoint}`, { ...tags, status: 'error' });
      throw error;
    }
  }

  // Get performance summary
  getSummary(timeWindow: number = 60 * 60 * 1000): any {
    const cutoff = new Date(Date.now() - timeWindow);
    const recentMetrics = this.metrics.filter(m => m.timestamp > cutoff);

    const summary: any = {
      totalMetrics: recentMetrics.length,
      timeWindow: `${timeWindow / 1000}s`,
      byName: {},
    };

    // Group by metric name
    const grouped = recentMetrics.reduce((acc, metric) => {
      if (!acc[metric.name]) {
        acc[metric.name] = [];
      }
      acc[metric.name].push(metric.value);
      return acc;
    }, {} as Record<string, number[]>);

    // Calculate statistics for each metric
    Object.entries(grouped).forEach(([name, values]) => {
      const sorted = values.sort((a, b) => a - b);
      summary.byName[name] = {
        count: values.length,
        min: Math.min(...values),
        max: Math.max(...values),
        avg: values.reduce((a, b) => a + b, 0) / values.length,
        p50: sorted[Math.floor(sorted.length * 0.5)],
        p95: sorted[Math.floor(sorted.length * 0.95)],
        p99: sorted[Math.floor(sorted.length * 0.99)],
      };
    });

    return summary;
  }
}

export const performanceMonitor = new PerformanceMonitor();

// Decorator for monitoring function performance
export function monitor(name?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const metricName = name || `${target.constructor.name}.${propertyKey}`;

    descriptor.value = async function (...args: any[]) {
      performanceMonitor.startTimer(metricName);
      
      try {
        const result = await originalMethod.apply(this, args);
        performanceMonitor.endTimer(metricName, { status: 'success' });
        return result;
      } catch (error) {
        performanceMonitor.endTimer(metricName, { status: 'error' });
        throw error;
      }
    };

    return descriptor;
  };
}

// Utility function for measuring execution time
export async function measureTime<T>(
  name: string,
  fn: () => Promise<T> | T,
  tags?: Record<string, string>
): Promise<{ result: T; duration: number }> {
  const start = Date.now();
  
  try {
    const result = await fn();
    const duration = Date.now() - start;
    
    performanceMonitor.recordMetric({
      name,
      value: duration,
      unit: 'ms',
      timestamp: new Date(),
      tags: { ...tags, status: 'success' },
    });

    return { result, duration };
  } catch (error) {
    const duration = Date.now() - start;
    
    performanceMonitor.recordMetric({
      name,
      value: duration,
      unit: 'ms',
      timestamp: new Date(),
      tags: { ...tags, status: 'error' },
    });

    throw error;
  }
}
