import bcrypt from 'bcryptjs';
import { db } from './db';
import { User, Role } from '@/types';

// 密码哈希
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
}

// 验证密码
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return await bcrypt.compare(password, hashedPassword);
}

// 创建用户
export async function createUser(userData: {
  email: string;
  username: string;
  password: string;
  firstName?: string;
  lastName?: string;
  role?: Role;
}): Promise<User | null> {
  try {
    const hashedPassword = await hashPassword(userData.password);
    
    const user = await db.user.create({
      data: {
        email: userData.email,
        username: userData.username,
        password: hashedPassword,
        firstName: userData.firstName,
        lastName: userData.lastName,
        role: userData.role || Role.USER,
      },
    });

    // 不返回密码
    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword as User;
  } catch (error) {
    console.error('Error creating user:', error);
    return null;
  }
}

// 通过邮箱查找用户
export async function getUserByEmail(email: string): Promise<User | null> {
  try {
    const user = await db.user.findUnique({
      where: { email },
    });

    if (!user) return null;

    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword as User;
  } catch (error) {
    console.error('Error finding user by email:', error);
    return null;
  }
}

// 通过用户名查找用户
export async function getUserByUsername(username: string): Promise<User | null> {
  try {
    const user = await db.user.findUnique({
      where: { username },
    });

    if (!user) return null;

    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword as User;
  } catch (error) {
    console.error('Error finding user by username:', error);
    return null;
  }
}

// 通过ID查找用户
export async function getUserById(id: string): Promise<User | null> {
  try {
    const user = await db.user.findUnique({
      where: { id },
    });

    if (!user) return null;

    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword as User;
  } catch (error) {
    console.error('Error finding user by ID:', error);
    return null;
  }
}

// 验证用户登录
export async function validateUser(email: string, password: string): Promise<User | null> {
  try {
    const user = await db.user.findUnique({
      where: { email },
    });

    if (!user) return null;

    const isValidPassword = await verifyPassword(password, user.password);
    if (!isValidPassword) return null;

    const { password: _, ...userWithoutPassword } = user;
    return userWithoutPassword as User;
  } catch (error) {
    console.error('Error validating user:', error);
    return null;
  }
}

// 更新用户信息
export async function updateUser(id: string, userData: Partial<User>): Promise<User | null> {
  try {
    const user = await db.user.update({
      where: { id },
      data: userData,
    });

    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword as User;
  } catch (error) {
    console.error('Error updating user:', error);
    return null;
  }
}

// 更新用户密码
export async function updateUserPassword(id: string, newPassword: string): Promise<boolean> {
  try {
    const hashedPassword = await hashPassword(newPassword);
    
    await db.user.update({
      where: { id },
      data: { password: hashedPassword },
    });

    return true;
  } catch (error) {
    console.error('Error updating user password:', error);
    return false;
  }
}

// 检查邮箱是否已存在
export async function isEmailTaken(email: string): Promise<boolean> {
  try {
    const user = await db.user.findUnique({
      where: { email },
      select: { id: true },
    });

    return !!user;
  } catch (error) {
    console.error('Error checking email:', error);
    return false;
  }
}

// 检查用户名是否已存在
export async function isUsernameTaken(username: string): Promise<boolean> {
  try {
    const user = await db.user.findUnique({
      where: { username },
      select: { id: true },
    });

    return !!user;
  } catch (error) {
    console.error('Error checking username:', error);
    return false;
  }
}

// 获取用户统计信息
export async function getUserStats(userId: string) {
  try {
    const [postsCount, commentsCount, filesCount] = await Promise.all([
      db.post.count({ where: { authorId: userId } }),
      db.comment.count({ where: { authorId: userId } }),
      db.file.count({ where: { uploadedBy: userId } }),
    ]);

    return {
      postsCount,
      commentsCount,
      filesCount,
    };
  } catch (error) {
    console.error('Error getting user stats:', error);
    return {
      postsCount: 0,
      commentsCount: 0,
      filesCount: 0,
    };
  }
}

// 检查用户权限
export function hasPermission(userRole: Role, requiredRole: Role): boolean {
  const roleHierarchy = {
    [Role.USER]: 0,
    [Role.MODERATOR]: 1,
    [Role.ADMIN]: 2,
    [Role.SUPER_ADMIN]: 3,
  };

  return roleHierarchy[userRole] >= roleHierarchy[requiredRole];
}

// 检查是否为管理员
export function isAdmin(userRole: Role): boolean {
  return userRole === Role.ADMIN || userRole === Role.SUPER_ADMIN;
}

// 检查是否为超级管理员
export function isSuperAdmin(userRole: Role): boolean {
  return userRole === Role.SUPER_ADMIN;
}

// 检查是否为版主或以上
export function isModerator(userRole: Role): boolean {
  return hasPermission(userRole, Role.MODERATOR);
}
