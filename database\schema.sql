-- 大型网站管理系统数据库结构
-- 创建时间: 2025-07-22
-- 支持功能: 用户管理、文件上传、公告系统、讨论区、管理员后台

-- 创建数据库
CREATE DATABASE IF NOT EXISTS aeep_website CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE aeep_website;

-- 用户表
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    nickname VARCHAR(100),
    avatar VARCHAR(255) DEFAULT '/uploads/avatars/default.png',
    bio TEXT,
    phone VARCHAR(20),
    status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
    email_verified BOOLEAN DEFAULT FALSE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status (status)
);

-- 管理员表
CREATE TABLE admins (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    role ENUM('super_admin', 'admin', 'moderator') DEFAULT 'moderator',
    permissions JSON,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_role (role)
);

-- 公告表
CREATE TABLE announcements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    author_id INT NOT NULL,
    category ENUM('general', 'important', 'maintenance', 'event') DEFAULT 'general',
    priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    featured BOOLEAN DEFAULT FALSE,
    view_count INT DEFAULT 0,
    attachment VARCHAR(255),
    publish_at TIMESTAMP NULL,
    expire_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_status (status),
    INDEX idx_category (category),
    INDEX idx_priority (priority),
    INDEX idx_publish_at (publish_at),
    INDEX idx_featured (featured)
);

-- 文件表
CREATE TABLE files (
    id INT PRIMARY KEY AUTO_INCREMENT,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    uploader_id INT NOT NULL,
    category ENUM('avatar', 'document', 'image', 'video', 'other') DEFAULT 'other',
    description TEXT,
    download_count INT DEFAULT 0,
    is_public BOOLEAN DEFAULT TRUE,
    status ENUM('active', 'deleted') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (uploader_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_uploader_id (uploader_id),
    INDEX idx_category (category),
    INDEX idx_file_type (file_type),
    INDEX idx_status (status),
    INDEX idx_is_public (is_public)
);

-- 讨论区分类表
CREATE TABLE forum_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(100),
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_active (is_active)
);

-- 讨论区主题表
CREATE TABLE forum_topics (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    author_id INT NOT NULL,
    is_pinned BOOLEAN DEFAULT FALSE,
    is_locked BOOLEAN DEFAULT FALSE,
    view_count INT DEFAULT 0,
    reply_count INT DEFAULT 0,
    last_reply_at TIMESTAMP NULL,
    last_reply_user_id INT NULL,
    status ENUM('active', 'hidden', 'deleted') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES forum_categories(id) ON DELETE CASCADE,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (last_reply_user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_category_id (category_id),
    INDEX idx_author_id (author_id),
    INDEX idx_is_pinned (is_pinned),
    INDEX idx_last_reply_at (last_reply_at),
    INDEX idx_status (status)
);

-- 系统日志表
CREATE TABLE system_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NULL,
    action VARCHAR(100) NOT NULL,
    module VARCHAR(50) NOT NULL,
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    request_data JSON,
    response_data JSON,
    status ENUM('success', 'error', 'warning') DEFAULT 'success',
    execution_time DECIMAL(10,3),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_module (module),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- 用户会话表
CREATE TABLE user_sessions (
    id VARCHAR(128) PRIMARY KEY,
    user_id INT NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at),
    INDEX idx_last_activity (last_activity)
);

-- 网站统计表
CREATE TABLE site_statistics (
    id INT PRIMARY KEY AUTO_INCREMENT,
    date DATE NOT NULL,
    total_users INT DEFAULT 0,
    active_users INT DEFAULT 0,
    new_users INT DEFAULT 0,
    total_posts INT DEFAULT 0,
    new_posts INT DEFAULT 0,
    total_files INT DEFAULT 0,
    new_files INT DEFAULT 0,
    page_views INT DEFAULT 0,
    unique_visitors INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY idx_date (date),
    INDEX idx_created_at (created_at)
);

-- 用户通知表
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    type ENUM('system', 'announcement', 'reply', 'mention', 'like') NOT NULL,
    title VARCHAR(200) NOT NULL,
    content TEXT,
    related_id INT NULL,
    related_type VARCHAR(50) NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at)
);

-- 系统配置表
CREATE TABLE system_config (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT,
    description TEXT,
    type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_config_key (config_key),
    INDEX idx_is_public (is_public)
);

-- 插入初始数据

-- 插入默认管理员用户
INSERT INTO users (username, email, password_hash, nickname, status, email_verified) VALUES
('admin', '<EMAIL>', '$2b$10$rQZ8kqXvJ5K5K5K5K5K5K5K5K5K5K5K5K5K5K5K5K5K5K5K5K5K5K5', '系统管理员', 'active', TRUE);

-- 插入管理员权限
INSERT INTO admins (user_id, role, permissions) VALUES
(1, 'super_admin', '{"users": "all", "content": "all", "system": "all", "logs": "all"}');

-- 插入默认论坛分类
INSERT INTO forum_categories (name, description, icon, sort_order) VALUES
('公告通知', '官方公告和重要通知', 'notification', 1),
('技术讨论', '技术相关话题讨论', 'code', 2),
('意见建议', '用户意见和建议', 'bulb', 3),
('闲聊灌水', '日常闲聊和交流', 'coffee', 4);

-- 插入系统配置
INSERT INTO system_config (config_key, config_value, description, type, is_public) VALUES
('site_name', 'AEEP大型网站系统', '网站名称', 'string', TRUE),
('site_description', '功能完整的大型网站管理系统', '网站描述', 'string', TRUE),
('max_file_size', '104857600', '最大文件上传大小(字节)', 'number', FALSE),
('allowed_file_types', '["jpg","jpeg","png","gif","pdf","doc","docx","txt","zip","rar"]', '允许的文件类型', 'json', FALSE),
('user_registration', 'true', '是否允许用户注册', 'boolean', TRUE),
('email_verification', 'true', '是否需要邮箱验证', 'boolean', FALSE),
('max_concurrent_users', '100', '最大并发用户数', 'number', FALSE);

-- 创建性能优化索引
CREATE INDEX idx_users_created_at ON users(created_at);
CREATE INDEX idx_announcements_created_at ON announcements(created_at);
CREATE INDEX idx_files_created_at ON files(created_at);
CREATE INDEX idx_forum_topics_created_at ON forum_topics(created_at);
CREATE INDEX idx_forum_replies_created_at ON forum_replies(created_at);

-- 创建复合索引
CREATE INDEX idx_announcements_status_publish ON announcements(status, publish_at);
CREATE INDEX idx_forum_topics_category_status ON forum_topics(category_id, status);
CREATE INDEX idx_files_uploader_category ON files(uploader_id, category);

-- 创建全文搜索索引
ALTER TABLE announcements ADD FULLTEXT(title, content);
ALTER TABLE forum_topics ADD FULLTEXT(title, content);
ALTER TABLE forum_replies ADD FULLTEXT(content);

-- 创建触发器更新统计数据
DELIMITER //

CREATE TRIGGER update_topic_reply_count
AFTER INSERT ON forum_replies
FOR EACH ROW
BEGIN
    UPDATE forum_topics
    SET reply_count = reply_count + 1,
        last_reply_at = NEW.created_at,
        last_reply_user_id = NEW.author_id
    WHERE id = NEW.topic_id;
END//

CREATE TRIGGER update_file_download_count
AFTER UPDATE ON files
FOR EACH ROW
BEGIN
    IF NEW.download_count > OLD.download_count THEN
        INSERT INTO system_logs (action, module, description, created_at)
        VALUES ('file_download', 'files', CONCAT('文件下载: ', NEW.original_name), NOW());
    END IF;
END//

DELIMITER ;

-- 创建视图
CREATE VIEW active_users AS
SELECT u.*, a.role as admin_role
FROM users u
LEFT JOIN admins a ON u.id = a.user_id
WHERE u.status = 'active';

CREATE VIEW latest_announcements AS
SELECT a.*, u.nickname as author_name
FROM announcements a
JOIN users u ON a.author_id = u.id
WHERE a.status = 'published'
AND (a.publish_at IS NULL OR a.publish_at <= NOW())
AND (a.expire_at IS NULL OR a.expire_at > NOW())
ORDER BY a.featured DESC, a.created_at DESC;

-- 数据库初始化完成

-- 讨论区回复表
CREATE TABLE forum_replies (
    id INT PRIMARY KEY AUTO_INCREMENT,
    topic_id INT NOT NULL,
    content TEXT NOT NULL,
    author_id INT NOT NULL,
    parent_id INT NULL,
    quote_id INT NULL,
    status ENUM('active', 'hidden', 'deleted') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (topic_id) REFERENCES forum_topics(id) ON DELETE CASCADE,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES forum_replies(id) ON DELETE CASCADE,
    FOREIGN KEY (quote_id) REFERENCES forum_replies(id) ON DELETE SET NULL,
    INDEX idx_topic_id (topic_id),
    INDEX idx_author_id (author_id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_status (status)
);
