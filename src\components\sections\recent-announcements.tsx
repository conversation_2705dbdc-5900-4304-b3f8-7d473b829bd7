"use client";

import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, <PERSON><PERSON>, <PERSON>R<PERSON> } from "lucide-react";
import { formatRelativeTime } from "@/lib/utils";

// 模拟数据
const mockAnnouncements = [
  {
    id: "1",
    title: "Platform Update v2.1 - New Features Released",
    excerpt: "We're excited to announce new features including enhanced file sharing, improved forum navigation, and better mobile experience.",
    isPinned: true,
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
    author: {
      username: "admin",
      avatar: null
    }
  },
  {
    id: "2", 
    title: "Community Guidelines Update",
    excerpt: "Please review our updated community guidelines to ensure a positive experience for all members.",
    isPinned: false,
    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
    author: {
      username: "moderator",
      avatar: null
    }
  },
  {
    id: "3",
    title: "Scheduled Maintenance - January 25th",
    excerpt: "We will be performing scheduled maintenance on January 25th from 2:00 AM to 4:00 AM UTC. The platform may be temporarily unavailable.",
    isPinned: false,
    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
    author: {
      username: "admin",
      avatar: null
    }
  }
];

export function RecentAnnouncements() {
  return (
    <section className="py-12">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h2 className="text-2xl font-bold tracking-tight sm:text-3xl mb-2">
            Latest Announcements
          </h2>
          <p className="text-muted-foreground">
            Stay updated with important news and updates
          </p>
        </div>
        <Button variant="outline" asChild>
          <Link href="/announcements">
            View All
            <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </div>

      <div className="grid gap-6">
        {mockAnnouncements.map((announcement) => (
          <Card key={announcement.id} className="card-hover">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-2">
                  <Bell className="h-5 w-5 text-primary" />
                  {announcement.isPinned && (
                    <Pin className="h-4 w-4 text-yellow-500" />
                  )}
                  <Badge variant={announcement.isPinned ? "default" : "secondary"}>
                    {announcement.isPinned ? "Pinned" : "Announcement"}
                  </Badge>
                </div>
                <span className="text-sm text-muted-foreground">
                  {formatRelativeTime(announcement.createdAt)}
                </span>
              </div>
              <CardTitle className="text-xl hover:text-primary transition-colors">
                <Link href={`/announcements/${announcement.id}`}>
                  {announcement.title}
                </Link>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-base leading-relaxed mb-4">
                {announcement.excerpt}
              </CardDescription>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <span>By {announcement.author.username}</span>
                </div>
                <Button variant="ghost" size="sm" asChild>
                  <Link href={`/announcements/${announcement.id}`}>
                    Read More
                    <ArrowRight className="ml-2 h-3 w-3" />
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </section>
  );
}
