{"name": "community-platform", "version": "1.0.0", "description": "A comprehensive community platform with file uploads, announcements, forums, and admin management", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio"}, "dependencies": {"@next/font": "^14.0.0", "@prisma/client": "^5.7.0", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@types/bcryptjs": "^2.4.6", "@types/multer": "^1.4.11", "@types/node": "^20.10.0", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "autoprefixer": "^10.4.16", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "eslint": "^8.55.0", "eslint-config-next": "^14.0.0", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "multer": "^1.4.5-lts.1", "next": "^14.0.0", "next-auth": "^4.24.5", "next-intl": "^3.3.0", "postcss": "^8.4.32", "prisma": "^5.7.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-quill": "^2.0.0", "sharp": "^0.33.0", "tailwind-merge": "^2.1.0", "tailwindcss": "^3.3.6", "tailwindcss-animate": "^1.0.7", "typescript": "^5.3.2", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "eslint": "^8.55.0", "eslint-config-next": "^14.0.0", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.3.2"}}